// SinoairAgent Side Panel - 独立生命周期
class SinoairSidePanel {
    constructor() {
        this.serverUrl = '';
        this.authToken = '';
        this.userInfo = null;
        this.selectedAgent = null;
        this.userPermissions = [];
        this.lastRecognitionResult = null;
        this.lastJsonData = null;
        this.currentBinding = null;
        this.lastRecognitionRecordId = null; // 最后一次识别记录的ID
        this.currentFillRecordId = null; // 当前回填记录的ID

        // 主题管理
        this.currentTheme = 'default';
        this.themeSwitcherCollapsed = true; // 默认收起

        // 加载状态管理
        this.loadingState = {
            isLoading: false,
            currentStep: 0,
            totalSteps: 1,
            message: '正在处理...'
        };

        // 权限加载防抖标志
        this.isLoadingPermissions = false;

        // 权限缓存（避免频繁请求）
        this.permissionCache = {
            data: [],
            timestamp: 0,
            ttl: 30000 // 30秒缓存时间
        };

        this.init();
    }

    async init() {
        console.log('🚀 SinoairAgent Side Panel 初始化...');

        // 加载保存的设置
        await this.loadSettings();

        // 绑定事件监听器
        this.bindEventListeners();

        // 监听来自Background的消息
        this.bindMessageListener();

        // 如果有token，验证其有效性
        if (this.authToken) {
            console.log('🔍 验证登录状态...');
            const isValid = await this.validateToken();
            if (!isValid) {
                console.log('❌ Token已过期，清除登录状态');
                this.authToken = '';
                this.userInfo = null;
                await chrome.storage.local.remove(['authToken', 'userInfo']);
            }
        }

        // 更新UI状态
        this.updateUI();

        // 如果已登录，加载权限和恢复状态
        if (this.authToken) {
            const permissionSuccess = await this.loadUserPermissions();
            if (permissionSuccess !== false) {
                // 权限加载成功，恢复其他状态
                await this.restoreAgentSelection();
                await this.restoreRecognitionData();
                // 注意：页面绑定不需要恢复，因为换页面后绑定应该重新查询
            } else {
                // 权限加载失败，清除登录状态
                console.warn('⚠️ 初始化时权限加载失败，清除登录状态');
                this.authToken = '';
                this.userInfo = null;
                await chrome.storage.local.remove(['authToken', 'userInfo']);
                this.updateUI();
            }
        }

        console.log('✅ Side Panel 初始化完成');
    }

    // 绑定消息监听器
    bindMessageListener() {
        console.log('🔧 Side Panel绑定消息监听器...');
        // 注意：不在这里添加监听器，使用全局监听器
    }

    // 验证token有效性
    async validateToken() {
        try {
            const response = await fetch(`${this.serverUrl}/api/v1/auth/validate`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 401) {
                return false; // token无效
            }

            if (response.ok) {
                const result = await response.json();
                return result.code === 200;
            }

            return false;
        } catch (error) {
            console.error('Token验证失败:', error);
            // 网络错误时假设token有效，让后续的权限加载来最终确定token状态
            // 如果token真的过期了，权限加载会返回401并触发handleTokenExpired
            return true;
        }
    }

    // 加载保存的设置
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['serverUrl', 'authToken', 'userInfo', 'currentTheme', 'themeSwitcherCollapsed']);

            this.serverUrl = result.serverUrl || 'http://172.17.0.137:8080';
            this.authToken = result.authToken || '';
            this.userInfo = result.userInfo || null;
            this.currentTheme = result.currentTheme || 'default';
            this.themeSwitcherCollapsed = result.themeSwitcherCollapsed !== undefined ? result.themeSwitcherCollapsed : true; // 默认收起

            console.log('📥 设置加载完成:', {
                serverUrl: this.serverUrl,
                hasToken: !!this.authToken,
                userInfo: this.userInfo,
                currentTheme: this.currentTheme
            });

            // 更新UI中的服务器地址
            const serverUrlInput = document.getElementById('serverUrl');
            if (serverUrlInput) {
                serverUrlInput.value = this.serverUrl;
            }

            // 应用主题
            this.applyTheme(this.currentTheme);

            // 更新主题切换器状态
            this.updateThemeSwitcherUI();

        } catch (error) {
            console.error('❌ 加载设置失败:', error);
        }
    }

    // 保存设置
    async saveSettings() {
        try {
            await chrome.storage.local.set({
                serverUrl: this.serverUrl,
                authToken: this.authToken,
                userInfo: this.userInfo
            });
            console.log('💾 设置保存成功');
        } catch (error) {
            console.error('❌ 保存设置失败:', error);
        }
    }

    // 绑定事件监听器
    bindEventListeners() {
        // 登录相关
        document.getElementById('loginBtn')?.addEventListener('click', () => this.handleLogin());
        document.getElementById('logoutBtn')?.addEventListener('click', () => this.handleLogout());
        
        // Agent相关
        document.getElementById('loadAgentsBtn')?.addEventListener('click', () => this.loadAgents());
        
        // 文档识别相关
        document.getElementById('captureBtn')?.addEventListener('click', () => this.captureScreen());
        document.getElementById('uploadBtn')?.addEventListener('click', () => this.uploadFile());
        document.getElementById('uploadJsonBtn')?.addEventListener('click', () => this.showJsonModal());
        document.getElementById('fileInput')?.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // 表单填充相关
        document.getElementById('checkBindingBtn')?.addEventListener('click', () => this.checkPageBinding());
        document.getElementById('fillFormBtn')?.addEventListener('click', () => this.autoFillForm());
        
        // 工具相关
        document.getElementById('detectFormBtn')?.addEventListener('click', () => this.detectForms());
        document.getElementById('extractDataBtn')?.addEventListener('click', () => this.extractData());
        document.getElementById('htmlExtractBtn')?.addEventListener('click', () => this.extractHTML());
        
        // 设置相关
        document.getElementById('clearStorageBtn')?.addEventListener('click', () => this.clearStorage());

        // JSON模态框相关
        document.getElementById('closeJsonModal')?.addEventListener('click', () => this.hideJsonModal());
        document.getElementById('cancelJsonBtn')?.addEventListener('click', () => this.hideJsonModal());
        document.getElementById('submitJsonBtn')?.addEventListener('click', () => this.submitJsonData());

        // 主题切换相关
        document.getElementById('themeSwitcherToggle')?.addEventListener('click', () => this.toggleThemeSwitcher());

        // 主题选项点击事件
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const theme = e.target.getAttribute('data-theme');
                this.switchTheme(theme);
            });
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement && activeElement.id === 'password') {
                    this.handleLogin();
                }
            }
        });
    }

    // 更新UI状态
    updateUI() {
        const isLoggedIn = !!this.authToken;
        
        // 更新登录状态
        const loginStatus = document.getElementById('loginStatus');
        const statusDot = loginStatus?.querySelector('.status-dot');
        const statusText = loginStatus?.querySelector('.status-text');
        
        if (statusDot && statusText) {
            if (isLoggedIn) {
                statusDot.className = 'status-dot success';
                statusText.textContent = '已登录，可以使用功能';
            } else {
                statusDot.className = 'status-dot error';
                statusText.textContent = '未登录，请先登录';
            }
        }
        
        // 显示/隐藏面板
        const loginForm = document.getElementById('loginForm');
        const mainPanel = document.getElementById('mainPanel');

        if (loginForm && mainPanel) {
            if (isLoggedIn) {
                loginForm.classList.add('hidden');
                mainPanel.classList.remove('hidden');
                // 登录后显示所有功能区域并加载权限
                this.showAllFeatureSections();
                // 权限加载会在loadUserPermissions中调用updateUIBasedOnPermissions
            } else {
                loginForm.classList.remove('hidden');
                mainPanel.classList.add('hidden');
                // 未登录时隐藏所有功能按钮
                this.hideAllFunctionButtons();
            }
        }
    }

    // 显示状态消息
    showStatus(type, message, duration = null) {
        const statusEl = document.getElementById('statusMessage');
        if (!statusEl) return;

        statusEl.className = `status-message ${type}`;
        statusEl.textContent = message;
        statusEl.style.display = 'block';

        // 清除之前的定时器
        if (this.statusTimer) {
            clearTimeout(this.statusTimer);
        }

        // 根据消息类型设置不同的默认持续时间
        if (duration === null) {
            switch (type) {
                case 'success':
                    duration = 3000; // 成功消息3秒
                    break;
                case 'info':
                    duration = 4000; // 信息消息4秒
                    break;
                case 'warning':
                    duration = 6000; // 警告消息6秒
                    break;
                case 'error':
                    duration = 8000; // 错误消息8秒
                    break;
                default:
                    duration = 4000; // 默认4秒
            }
        }

        // 设置自动隐藏
        this.statusTimer = setTimeout(() => {
            statusEl.style.display = 'none';
        }, duration);
    }

    // 获取当前活动标签页
    async getCurrentTab() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        return tab;
    }

    // 发送消息给Content Script
    async sendMessageToContent(message) {
        let tab = null;
        try {
            tab = await this.getCurrentTab();
            if (!tab) {
                throw new Error('无法获取当前标签页');
            }

            const response = await chrome.tabs.sendMessage(tab.id, message);
            return response;
        } catch (error) {
            console.error('❌ 发送消息失败:', error);

            // 检查是否是连接错误或页面脚本问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('请刷新主页面后重试') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                // 确保tab存在再调用handleConnectionError
                if (tab) {
                    return this.handleConnectionError(tab);
                } else {
                    // 如果tab都获取不到，返回一个友好的错误
                    throw new Error('无法连接到页面，请确保页面已加载完成');
                }
            }

            throw error;
        }
    }

    // 处理连接错误（Content Script未注入）
    async handleConnectionError(tab) {
        console.log('🔄 检测到连接错误，尝试重新注入Content Script...');

        try {
            // 尝试重新注入Content Script
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content.js']
            });

            // 等待一下让Content Script初始化
            await new Promise(resolve => setTimeout(resolve, 500));

            this.showStatus('success', '页面脚本已重新加载，请重试操作');
            return { success: false, message: '页面脚本已重新加载，请重试操作', needRetry: true };

        } catch (injectError) {
            console.error('❌ 重新注入Content Script失败:', injectError);

            // 显示友好的错误提示
            this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');

            // 显示详细的解决方案
            this.showConnectionErrorModal();

            return {
                success: false,
                message: '页面连接异常，请刷新当前页面后重试',
                needRefresh: true
            };
        }
    }

    // 显示连接错误的解决方案模态框
    showConnectionErrorModal() {
        const modalHtml = `
            <div class="modal fade show" id="connectionErrorModal" style="display: block; background: rgba(0,0,0,0.5); position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 9999;">
                <div class="modal-dialog" style="display: flex; align-items: center; justify-content: center; height: 100%; margin: 0; padding: 20px;">
                    <div class="modal-content" style="background: #fff; color: #333; border-radius: 8px; max-width: 420px; width: 100%; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                        <div class="modal-header" style="padding: 16px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                            <h5 style="margin: 0; color: #333;">💡 需要刷新页面</h5>
                            <button type="button" class="close-modal-btn" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666; padding: 0; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">&times;</button>
                        </div>
                        <div class="modal-body" style="padding: 16px;">
                            <p style="margin: 0 0 12px 0;">当前页面与插件的连接出现异常，需要刷新页面来重新建立连接。</p>
                            <p style="margin: 0 0 16px 0; font-weight: bold; color: #007bff;">请按以下步骤操作：</p>
                            <div style="background: #f8f9fa; padding: 12px; border-radius: 4px; margin-bottom: 16px;">
                                <div style="margin-bottom: 8px;">🔄 <strong>刷新当前页面</strong></div>
                                <div style="font-size: 13px; color: #666; margin-left: 20px;">
                                    • 按 F5 键<br>
                                    • 或按 Ctrl+R (Windows) / Cmd+R (Mac)<br>
                                    • 或点击浏览器的刷新按钮
                                </div>
                            </div>
                            <div style="font-size: 13px; color: #666;">
                                刷新后即可正常使用所有插件功能
                            </div>
                        </div>
                        <div class="modal-footer" style="padding: 16px; border-top: 1px solid #eee; text-align: right;">
                            <button class="close-modal-btn" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">我知道了</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('connectionErrorModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定关闭按钮事件
        const modal = document.getElementById('connectionErrorModal');
        if (modal) {
            const closeButtons = modal.querySelectorAll('.close-modal-btn');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.remove();
                });
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 10秒后自动关闭
        setTimeout(() => {
            const modal = document.getElementById('connectionErrorModal');
            if (modal) {
                modal.remove();
            }
        }, 10000);
    }

    // 处理登录
    async handleLogin() {
        const serverUrl = document.getElementById('serverUrl')?.value?.trim();
        const username = document.getElementById('username')?.value?.trim();
        const password = document.getElementById('password')?.value?.trim();

        if (!serverUrl || !username || !password) {
            this.showStatus('error', '请填写完整的登录信息');
            return;
        }

        try {
            this.showStatus('info', '正在登录...');

            const response = await fetch(`${serverUrl}/api/v1/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.serverUrl = serverUrl;
                this.authToken = result.data.token;
                this.userInfo = result.data.userInfo || { username };

                // 保存设置
                await this.saveSettings();

                // 显示权限加载遮罩
                this.showLoading('正在加载用户权限...');

                // 检查混合内容问题并显示警告
                if (serverUrl.startsWith('http://')) {
                    this.showStatus('warning', '⚠️ 注意：服务器使用HTTP协议，在HTTPS网站上可能无法正常工作。建议联系管理员配置HTTPS服务器。', 8000);
                }

                // 加载用户权限，成功后才显示主界面
                const permissionSuccess = await this.loadUserPermissionsWithUI();

                if (permissionSuccess) {
                    // 权限加载成功，显示主界面
                    this.updateUI();
                    this.hideLoading();
                    this.showStatus('success', '登录成功');
                } else {
                    // 权限加载失败，回到登录页面
                    this.hideLoading();
                    this.handlePermissionLoadFailure();
                }
            } else {
                this.showStatus('error', `登录失败: ${result.message}`);
            }
        } catch (error) {
            console.error('登录失败:', error);
            this.showStatus('error', `登录失败: ${error.message}`);
        }
    }

    // 处理退出登录
    async handleLogout() {
        this.authToken = '';
        this.selectedAgent = null;
        this.userPermissions = [];
        this.userInfo = null;

        // 清除存储
        await chrome.storage.local.remove(['authToken', 'userInfo']);

        // 更新UI
        this.updateUI();
        
        this.showStatus('success', '已退出登录');
    }

    // 专门用于登录后的权限加载，返回成功/失败状态
    async loadUserPermissionsWithUI() {
        try {
            const success = await this.loadUserPermissions(true); // 强制刷新
            return success !== false; // 如果返回false表示失败，其他情况表示成功
        } catch (error) {
            console.error('❌ 登录后权限加载失败:', error);
            return false;
        }
    }

    // 加载用户权限
    async loadUserPermissions(forceRefresh = false) {
        try {
            if (!this.authToken) {
                console.log('🔒 未登录，跳过权限加载');
                this.userPermissions = [];
                return true; // 未登录不算失败
            }

            // 检查缓存（除非强制刷新）
            if (!forceRefresh && this.isPermissionCacheValid()) {
                console.log('📦 使用权限缓存数据');
                this.userPermissions = this.permissionCache.data;
                this.updateUIBasedOnPermissions();
                return true; // 缓存命中算成功
            }

            // 防抖机制：如果正在加载权限，跳过重复请求
            if (this.isLoadingPermissions) {
                console.log('🔄 权限正在加载中，跳过重复请求');
                return true; // 正在加载不算失败
            }

            this.isLoadingPermissions = true;
            console.log('🔑 开始加载用户权限...');

            // 创建超时控制器（兼容性更好）
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

            const response = await fetch(`${this.serverUrl}/api/v1/plugin/permissions`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId); // 清除超时定时器

            // 检查401错误（登录过期）
            if (response.status === 401) {
                console.warn('🔒 权限加载返回401，token已过期');
                this.handleTokenExpired();
                return false; // 认证失败
            }

            if (response.ok) {
                const data = await response.json();
                console.log('🔑 权限接口响应数据:', data);

                if (data.code === 200) {
                    this.userPermissions = data.data || [];
                    console.log('✅ 用户权限加载成功:', this.userPermissions);

                    // 更新权限缓存
                    this.updatePermissionCache(this.userPermissions);

                    // 更新UI权限显示
                    this.updateUIBasedOnPermissions();

                    return true; // 成功
                } else {
                    console.error('❌ 权限接口返回错误:', data);
                    throw new Error(data.message || '获取权限失败');
                }
            } else {
                const errorText = await response.text();
                console.error('❌ 权限接口HTTP错误:', response.status, errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('❌ 加载用户权限失败:', error);

            // 如果是网络相关错误，保持当前权限状态
            if (error.name === 'AbortError' ||
                error.name === 'TimeoutError' ||
                error.message.includes('fetch') ||
                error.message.includes('network') ||
                error.message.includes('Failed to fetch')) {
                console.warn('⚠️ 网络问题导致权限加载失败，保持当前UI状态');

                // 如果之前没有权限数据，网络错误时返回失败（让登录流程处理）
                if (this.userPermissions.length === 0) {
                    console.warn('⚠️ 首次权限加载失败，返回失败状态');
                    return false; // 返回失败，让登录流程处理
                }
                // 如果之前有权限数据，保持不变
                return true;
            } else {
                // 只有明确的认证错误才隐藏功能
                console.error('🔒 认证错误，隐藏权限功能');
                this.userPermissions = [];
                this.hidePermissionBasedFeatures();
                return false; // 认证错误返回失败
            }
        } finally {
            this.isLoadingPermissions = false;
        }

        return true; // 默认返回成功
    }

    // 检查权限缓存是否有效
    isPermissionCacheValid() {
        const now = Date.now();
        return this.permissionCache.timestamp > 0 &&
               (now - this.permissionCache.timestamp) < this.permissionCache.ttl;
    }

    // 更新权限缓存
    updatePermissionCache(permissions) {
        this.permissionCache.data = permissions || [];
        this.permissionCache.timestamp = Date.now();
        console.log('📦 权限缓存已更新:', this.permissionCache.data);
    }

    // 清除权限缓存
    clearPermissionCache() {
        this.permissionCache.data = [];
        this.permissionCache.timestamp = 0;
        console.log('🗑️ 权限缓存已清除');
    }

    // 检查是否有指定权限
    hasPermission(permission) {
        return this.userPermissions.includes(permission);
    }

    // 根据权限更新UI
    updateUIBasedOnPermissions() {
        console.log('🔄 根据权限更新UI...');

        // 权限映射
        const permissionMappings = [
            {
                permission: 'plugin:data:recognize',
                buttons: ['captureBtn', 'uploadBtn']
            },
            {
                permission: 'plugin:data:upload',
                buttons: ['uploadJsonBtn']
            },
            {
                permission: 'plugin:form:binding',
                buttons: ['checkBindingBtn']
            },
            {
                permission: 'plugin:form:fill',
                buttons: ['fillFormBtn']
            },
            {
                permission: 'plugin:form:detect',
                buttons: ['detectFormBtn']
            },
            {
                permission: 'plugin:form:extract',
                buttons: ['extractDataBtn']
            },
            {
                permission: 'plugin:html:extract',
                buttons: ['htmlExtractBtn']
            }
        ];

        // 始终显示的按钮（不需要特殊权限）
        const alwaysVisibleButtons = [
            'loadAgentsBtn',           // Agent加载
            'clearStorageBtn',         // 清除存储
            'logoutBtn'                // 退出登录
        ];

        // 更新按钮显示状态
        permissionMappings.forEach(mapping => {
            const hasPermission = this.hasPermission(mapping.permission);
            console.log(`🔑 权限检查: ${mapping.permission} = ${hasPermission}`);

            mapping.buttons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    if (hasPermission) {
                        this.showButton(btn);
                    } else {
                        this.hideButton(btn);
                    }
                }
            });
        });

        // 显示始终可见的按钮（登录后）
        if (this.authToken) {
            alwaysVisibleButtons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    console.log(`🔓 显示始终可见按钮: ${btnId}`);
                    this.showButton(btn);
                }
            });
        }

        // 隐藏空的功能区
        this.hideEmptyFeatureSections();

        console.log('✅ UI权限更新完成');
    }

    // 隐藏没有权限的功能
    hidePermissionBasedFeatures() {
        console.log('🔒 隐藏所有需要权限的功能...');

        const permissionButtons = [
            'captureBtn', 'uploadBtn', 'uploadJsonBtn',
            'fillFormBtn', 'checkBindingBtn',
            'htmlExtractBtn',
            'detectFormBtn', 'extractDataBtn'
        ];

        permissionButtons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                this.hideButton(btn);
            }
        });

        this.hideEmptyFeatureSections();
    }

    // 显示按钮
    showButton(btn) {
        btn.style.display = 'block';
        btn.style.visibility = 'visible';
        btn.style.opacity = '1';
        btn.style.pointerEvents = 'auto';
    }

    // 隐藏按钮
    hideButton(btn) {
        btn.style.display = 'none';
        btn.style.visibility = 'hidden';
        btn.style.opacity = '0';
        btn.style.pointerEvents = 'none';
    }

    // 隐藏空的功能区
    hideEmptyFeatureSections() {
        const featureSections = [
            {
                title: '文档识别',
                buttons: ['captureBtn', 'uploadBtn', 'uploadJsonBtn']
            },
            {
                title: '自动回填',
                buttons: ['fillFormBtn', 'checkBindingBtn']
            },
            {
                title: '工具',
                buttons: ['detectFormBtn', 'extractDataBtn', 'htmlExtractBtn']
            }
        ];

        featureSections.forEach(section => {
            const visibleButtons = section.buttons.filter(btnId => {
                const btn = document.getElementById(btnId);
                if (!btn) return false;
                const style = window.getComputedStyle(btn);
                return style.display !== 'none' && style.visibility !== 'hidden';
            });

            // 找到包含这些按钮的功能区
            const sectionElement = section.buttons.map(btnId => {
                const btn = document.getElementById(btnId);
                return btn ? btn.closest('.feature-section') : null;
            }).find(el => el !== null);

            if (sectionElement) {
                if (visibleButtons.length > 0) {
                    sectionElement.classList.remove('hidden');
                    sectionElement.style.display = 'block';
                    console.log(`✅ 显示功能区: ${section.title}`);
                } else {
                    sectionElement.classList.add('hidden');
                    sectionElement.style.display = 'none';
                    console.log(`❌ 隐藏空功能区: ${section.title}`);
                }
            }
        });
    }

    // 通用API调用方法，自动处理401错误
    async apiCall(url, options = {}) {
        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            // 检查401错误（未授权）
            if (response.status === 401) {
                console.warn('🔒 API调用返回401，token可能已过期');
                this.handleTokenExpired();
                throw new Error('登录已过期，请重新登录');
            }

            return response;
        } catch (error) {
            if (error.message.includes('登录已过期')) {
                throw error;
            }
            console.error('API调用失败:', error);
            throw error;
        }
    }

    // 直接调用API检查页面绑定（在Side Panel中执行，避免混合内容问题）
    async performPageBindingApiCall(currentUrl, agent) {
        try {
            console.log('🔍 Side Panel直接调用API检查页面绑定...');
            console.log('🌐 目标URL:', currentUrl);
            console.log('🤖 使用Agent:', agent?.agentName);

            if (!agent || !agent.id) {
                throw new Error('Agent信息缺失，请先选择Agent');
            }

            // 使用Side Panel的apiCall方法，避免混合内容问题
            const response = await this.apiCall(`${this.serverUrl}/api/v1/page-bindings/auto-fill-data?url=${encodeURIComponent(currentUrl)}&agentId=${agent.id}`);
            const result = await response.json();

            console.log('📋 页面绑定API调用结果:', result);
            return result;
        } catch (error) {
            console.error('❌ 页面绑定API调用失败:', error);
            throw error;
        }
    }

    // 处理权限加载失败
    handlePermissionLoadFailure() {
        console.log('❌ 权限加载失败，回到登录页面');

        // 清除登录状态
        this.authToken = '';
        this.userInfo = null;
        this.selectedAgent = null;
        this.userPermissions = [];

        // 清除权限缓存
        this.clearPermissionCache();

        // 清除存储
        chrome.storage.local.remove(['authToken', 'userInfo']);

        // 更新UI，显示登录表单
        this.updateUI();

        // 显示权限加载失败的提示
        this.showStatus('error', '权限加载失败，请重新登录', 5000);
    }

    // 处理token过期
    handleTokenExpired() {
        console.log('🔒 处理token过期，清除登录状态');

        // 清除登录状态
        this.authToken = '';
        this.userInfo = null;
        this.selectedAgent = null;
        this.userPermissions = [];

        // 清除权限缓存
        this.clearPermissionCache();

        // 清除存储
        chrome.storage.local.remove(['authToken', 'userInfo']);

        // 更新UI，显示登录表单
        this.updateUI();

        // 显示友好提示
        this.showStatus('warning', '登录已过期，请重新登录');
    }

    // 加载Agent列表
    async loadAgents() {
        try {
            this.showStatus('info', '正在加载Agent列表...');

            // 只获取已发布的Agent (status=3表示已发布)
            let response = await this.apiCall(`${this.serverUrl}/api/v1/agents?page=1&size=50&status=3`);
            let result = await response.json();
            console.log('🔍 Agent列表API响应:', result);

            if (result.code === 200) {
                let agents = [];

                // 处理不同的数据结构
                if (result.data) {
                    if (Array.isArray(result.data)) {
                        agents = result.data;
                    } else if (result.data.records && Array.isArray(result.data.records)) {
                        agents = result.data.records;
                    } else if (result.data.list && Array.isArray(result.data.list)) {
                        agents = result.data.list;
                    } else {
                        console.warn('⚠️ 未知的Agent数据结构:', result.data);
                        agents = [];
                    }
                }

                console.log('📋 获取到的Agent列表:', agents);
                this.displayAgents(agents);
                this.showStatus('success', `加载完成，共${agents.length}个Agent`);
            } else {
                console.error('❌ Agent列表API返回错误:', result);
                this.showStatus('error', `加载失败: ${result.message}`);
            }
        } catch (error) {
            console.error('加载Agent失败:', error);
            this.showStatus('error', `加载失败: ${error.message}`);
        }
    }

    // 显示Agent列表
    displayAgents(agents) {
        const agentList = document.getElementById('agentList');
        if (!agentList) return;

        agentList.innerHTML = '';

        // 确保agents是数组
        if (!Array.isArray(agents)) {
            console.error('❌ agents不是数组:', agents);
            agentList.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 20px; color: #dc3545;">Agent数据格式错误</div>';
            return;
        }

        if (agents.length === 0) {
            agentList.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 20px;">暂无可用的Agent</div>';
            return;
        }

        // 保存所有Agent数据用于搜索
        this.allAgents = agents;

        // 显示搜索框
        const searchContainer = document.getElementById('agentSearchContainer');
        if (searchContainer && agents.length > 0) {
            searchContainer.style.display = 'block';
            this.setupAgentSearch();
        }

        // 检查当前选中的Agent是否还存在
        if (this.selectedAgent) {
            const stillExists = agents.find(agent => agent.id === this.selectedAgent.id);
            if (!stillExists) {
                // 如果当前选中的Agent不存在了，清除选择
                this.clearAgentSelection();
                this.showStatus('warning', '之前选择的Agent已不可用，请重新选择');
            }
        }

        this.renderAgentList(agents);
    }

    // 更新识别状态显示
    updateRecognitionStatus(statusText, type = 'success', timingText = null) {
        const recognitionStatus = document.getElementById('recognitionStatus');
        const recognitionStatusText = document.getElementById('recognitionStatusText');
        const recognitionTiming = document.getElementById('recognitionTiming');
        const recognitionTimingText = document.getElementById('recognitionTimingText');
        const recognitionActions = document.getElementById('recognitionActions');
        const activeRecognitionButton = document.getElementById('activeRecognitionButton');

        if (statusText) {
            // 显示识别状态区域
            if (recognitionStatus) {
                recognitionStatus.style.display = 'block';
            }

            // 隐藏识别操作区域
            if (recognitionActions) {
                recognitionActions.classList.add('hidden');
            }

            // 显示保留的按钮
            this.showActiveRecognitionButton(type);

            // 更新状态文本
            if (recognitionStatusText) {
                recognitionStatusText.textContent = statusText;
                recognitionStatusText.title = statusText; // 添加悬停提示
            }

            // 显示或隐藏耗时信息
            if (timingText && recognitionTimingText && recognitionTiming) {
                recognitionTimingText.textContent = timingText;
                recognitionTiming.style.display = 'flex';
            } else if (recognitionTiming) {
                recognitionTiming.style.display = 'none';
            }

            // 绑定查看结果按钮事件
            this.bindRecognitionActionButtons();
        } else {
            // 隐藏识别状态区域
            if (recognitionStatus) {
                recognitionStatus.style.display = 'none';
            }

            // 隐藏保留的按钮
            if (activeRecognitionButton) {
                activeRecognitionButton.style.display = 'none';
                activeRecognitionButton.innerHTML = '';
            }

            // 显示识别操作区域
            if (recognitionActions) {
                recognitionActions.classList.remove('hidden');
            }
        }
    }

    // 显示保留的识别按钮
    showActiveRecognitionButton(type) {
        const activeRecognitionButton = document.getElementById('activeRecognitionButton');
        if (!activeRecognitionButton) return;

        let buttonHtml = '';

        switch (type) {
            case 'screenshot':
                buttonHtml = '<button id="activeScreenshotBtn" class="primary-button">📷 截图识别</button>';
                break;
            case 'file':
                buttonHtml = '<button id="activeUploadBtn" class="primary-button">📁 上传文件识别</button>';
                break;
            case 'json':
                buttonHtml = '<button id="activeJsonBtn" class="secondary-button">📝 上传JSON数据</button>';
                break;
            default:
                return;
        }

        activeRecognitionButton.innerHTML = buttonHtml;
        activeRecognitionButton.style.display = 'block';

        // 绑定按钮事件
        this.bindActiveRecognitionButtons(type);
    }

    // 绑定保留按钮的事件
    bindActiveRecognitionButtons(type) {
        switch (type) {
            case 'screenshot':
                const activeScreenshotBtn = document.getElementById('activeScreenshotBtn');
                if (activeScreenshotBtn) {
                    activeScreenshotBtn.onclick = () => this.captureScreen();
                }
                break;
            case 'file':
                const activeUploadBtn = document.getElementById('activeUploadBtn');
                if (activeUploadBtn) {
                    activeUploadBtn.onclick = () => this.uploadFile();
                }
                break;
            case 'json':
                const activeJsonBtn = document.getElementById('activeJsonBtn');
                if (activeJsonBtn) {
                    activeJsonBtn.onclick = () => this.showJsonModal();
                }
                break;
        }
    }

    // 绑定识别操作按钮事件
    bindRecognitionActionButtons() {
        const viewResultBtn = document.getElementById('viewResultBtn');
        const reRecognizeBtn = document.getElementById('reRecognizeBtn');

        if (viewResultBtn) {
            viewResultBtn.onclick = () => this.viewRecognitionResult();
        }

        if (reRecognizeBtn) {
            reRecognizeBtn.onclick = () => this.showRecognitionActions();
        }
    }

    // 查看识别结果
    viewRecognitionResult() {
        let resultData = null;
        let dataType = '';

        // 根据当前的识别类型来决定显示哪个数据
        const currentRecognitionType = this.getCurrentRecognitionType();

        if (currentRecognitionType === 'json' && this.lastJsonData) {
            resultData = this.lastJsonData;
            dataType = 'JSON数据';
        } else if (this.lastRecognitionResult) {
            // 如果有识别结果，优先显示result字段的内容
            if (this.lastRecognitionResult.result) {
                resultData = this.lastRecognitionResult.result;
                dataType = '文档识别结果';
            } else {
                // 如果没有result字段，显示完整的识别结果
                resultData = this.lastRecognitionResult;
                dataType = '文档识别结果（完整）';
            }
        }

        if (resultData) {
            this.showResultModal(dataType, resultData);
        } else {
            this.showStatus('error', '没有可查看的结果');
        }
    }

    // 获取当前识别类型
    getCurrentRecognitionType() {
        // 从本地存储获取当前的识别类型
        try {
            const stored = localStorage.getItem('currentRecognitionType');
            return stored || 'document';
        } catch (error) {
            return 'document';
        }
    }

    // 设置当前识别类型
    setCurrentRecognitionType(type) {
        try {
            localStorage.setItem('currentRecognitionType', type);
        } catch (error) {
            console.error('设置识别类型失败:', error);
        }
    }

    // 显示识别操作区域（重新识别）
    showRecognitionActions() {
        // 清除所有识别结果数据
        this.lastRecognitionResult = null;
        this.lastJsonData = null;
        this.lastUploadedFile = null;
        this.lastScreenshotData = null;
        this.lastRecognitionRecordId = null;
        this.currentFillRecordId = null;

        // 重置到初始状态：隐藏状态区域，显示操作区域
        this.updateRecognitionStatus(null);

        // 显示提示信息
        this.showStatus('info', '已清除识别结果，请重新选择识别方式', 3000);
    }

    // 显示结果模态框
    showResultModal(title, data) {
        const jsonString = JSON.stringify(data, null, 2);
        const modalHtml = `
            <div class="themed-result-modal" id="resultModal">
                <div class="themed-modal-dialog">
                    <div class="themed-modal-content">
                        <div class="themed-modal-header">
                            <h5 class="themed-modal-title">${title}</h5>
                            <button type="button" class="themed-close-btn close-result-modal">×</button>
                        </div>
                        <div class="themed-modal-body">
                            <pre class="themed-code-block" id="resultCodeBlock">${jsonString}</pre>
                        </div>
                        <div class="themed-modal-footer">
                            <div></div> <!-- 左侧占位 -->
                            <div class="modal-footer-buttons">
                                <button class="themed-copy-button" id="copyResultButton">
                                    📋 复制
                                </button>
                                <button class="themed-close-button close-result-modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('resultModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定关闭按钮事件
        const modal = document.getElementById('resultModal');
        if (modal) {
            const closeButtons = modal.querySelectorAll('.close-result-modal');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.remove();
                });
            });

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });

            // 绑定复制按钮事件
            const copyButton = modal.querySelector('#copyResultButton');
            if (copyButton) {
                copyButton.addEventListener('click', async () => {
                    try {
                        await navigator.clipboard.writeText(jsonString);

                        // 更新按钮状态
                        const originalText = copyButton.textContent;
                        copyButton.classList.add('copied');
                        copyButton.textContent = '✓ 已复制';

                        // 2秒后恢复原状
                        setTimeout(() => {
                            copyButton.classList.remove('copied');
                            copyButton.textContent = originalText;
                        }, 2000);

                    } catch (err) {
                        console.error('复制失败:', err);
                        // 降级方案：选择文本
                        const codeBlock = modal.querySelector('#resultCodeBlock');
                        if (codeBlock) {
                            const range = document.createRange();
                            range.selectNodeContents(codeBlock);
                            const selection = window.getSelection();
                            selection.removeAllRanges();
                            selection.addRange(range);
                        }
                    }
                });
            }
        }
    }

    // 更新绑定状态显示
    updateBindingStatus(statusText, bindingData = null) {
        console.log('🔄 更新绑定状态:', statusText, bindingData ? '有数据' : '无数据');

        const bindingStatus = document.getElementById('bindingStatus');
        const bindingStatusText = document.getElementById('bindingStatusText');
        const checkBindingBtn = document.getElementById('checkBindingBtn');

        if (!bindingStatus || !bindingStatusText || !checkBindingBtn) {
            console.error('❌ 绑定状态DOM元素未找到:', {
                bindingStatus: !!bindingStatus,
                bindingStatusText: !!bindingStatusText,
                checkBindingBtn: !!checkBindingBtn
            });
            return;
        }

        if (statusText) {
            console.log('✅ 显示绑定状态:', statusText);

            // 设置状态文本
            bindingStatusText.textContent = statusText;

            // 显示状态区域
            bindingStatus.style.display = 'block';

            // 隐藏检查绑定按钮
            checkBindingBtn.style.display = 'none';

            // 保存绑定数据
            this.currentBinding = bindingData;

            // 绑定按钮事件
            this.bindBindingActionButtons();

            console.log('✅ 绑定状态更新完成');
        } else {
            console.log('❌ 清除绑定状态');

            // 隐藏状态区域
            bindingStatus.style.display = 'none';

            // 显示检查绑定按钮
            checkBindingBtn.style.display = 'block';

            // 清除绑定数据
            this.currentBinding = null;
        }
    }

    // 绑定页面绑定操作按钮事件
    bindBindingActionButtons() {
        const changeBindingBtn = document.getElementById('changeBindingBtn');

        if (changeBindingBtn) {
            changeBindingBtn.onclick = () => this.showBindingSelection();
        }
    }

    // 显示绑定选择
    showBindingSelection() {
        // 清除当前绑定状态，重新检查页面绑定
        this.updateBindingStatus(null);
        this.checkPageBinding();
    }

    // 恢复Agent选择状态
    async restoreAgentSelection() {
        try {
            const result = await chrome.storage.local.get(['selectedAgent']);
            if (result.selectedAgent) {
                this.selectedAgent = result.selectedAgent;
                this.updateAgentStatus(this.selectedAgent);

                // 恢复Agent选择后，重置页面绑定状态
                // 因为可能切换了页面，之前的绑定配置不再适用
                this.resetPageBindingState();

                console.log('🔄 恢复Agent选择:', this.selectedAgent.agentName);
            }
        } catch (error) {
            console.error('恢复Agent选择失败:', error);
        }
    }

    // 保存Agent选择状态
    async saveAgentSelection() {
        try {
            await chrome.storage.local.set({
                selectedAgent: this.selectedAgent
            });
        } catch (error) {
            console.error('保存Agent选择失败:', error);
        }
    }

    // 恢复识别数据状态
    async restoreRecognitionData() {
        try {
            const result = await chrome.storage.local.get(['lastRecognitionResult', 'lastJsonData', 'lastRecognitionRecordId', 'recognitionType']);

            if (result.lastRecognitionResult) {
                this.lastRecognitionResult = result.lastRecognitionResult;
                console.log('🔄 恢复文档识别结果');
            }

            if (result.lastJsonData) {
                this.lastJsonData = result.lastJsonData;
                console.log('🔄 恢复JSON数据');
            }

            if (result.lastRecognitionRecordId) {
                this.lastRecognitionRecordId = result.lastRecognitionRecordId;
                console.log('🔄 恢复识别记录ID:', result.lastRecognitionRecordId);
            }

            // 恢复识别状态显示
            if (result.recognitionType) {
                let statusText = '';
                if (result.recognitionType === 'document' && this.lastRecognitionResult) {
                    statusText = '文档识别已完成';
                } else if (result.recognitionType === 'json' && this.lastJsonData) {
                    statusText = 'JSON数据已上传';
                }

                if (statusText) {
                    this.updateRecognitionStatus(statusText, result.recognitionType);
                }
            }
        } catch (error) {
            console.error('恢复识别数据失败:', error);
        }
    }

    // 保存识别数据状态
    async saveRecognitionData(type = null) {
        try {
            await chrome.storage.local.set({
                lastRecognitionResult: this.lastRecognitionResult,
                lastJsonData: this.lastJsonData,
                lastRecognitionRecordId: this.lastRecognitionRecordId,
                recognitionType: type
            });
        } catch (error) {
            console.error('保存识别数据失败:', error);
        }
    }



    // 渲染Agent列表
    renderAgentList(agents) {
        const agentList = document.getElementById('agentList');
        if (!agentList) return;

        agentList.innerHTML = '';

        agents.forEach(agent => {
            const agentItem = document.createElement('div');
            agentItem.className = 'agent-item';

            // 检查是否是当前选中的Agent
            if (this.selectedAgent && this.selectedAgent.id === agent.id) {
                agentItem.classList.add('selected');
            }

            agentItem.innerHTML = `
                <div class="agent-name">${agent.agentName}</div>
                <div class="agent-description">${agent.description || '无描述'}</div>
            `;

            agentItem.addEventListener('click', () => {
                this.selectAgent(agent, agentItem);
            });

            agentList.appendChild(agentItem);
        });
    }

    // 设置Agent搜索功能
    setupAgentSearch() {
        const searchInput = document.getElementById('agentSearchInput');
        if (!searchInput) return;

        // 移除之前的事件监听器
        searchInput.removeEventListener('input', this.handleAgentSearch);

        // 绑定搜索事件
        this.handleAgentSearch = (e) => {
            const searchTerm = e.target.value.toLowerCase().trim();

            if (!searchTerm) {
                // 显示所有Agent
                this.renderAgentList(this.allAgents);
                return;
            }

            // 过滤Agent
            const filteredAgents = this.allAgents.filter(agent =>
                agent.agentName.toLowerCase().includes(searchTerm) ||
                (agent.description && agent.description.toLowerCase().includes(searchTerm)) ||
                (agent.agentCode && agent.agentCode.toLowerCase().includes(searchTerm))
            );

            this.renderAgentList(filteredAgents);
        };

        searchInput.addEventListener('input', this.handleAgentSearch);
    }

    // 选择Agent
    selectAgent(agent, agentItem) {
        // 移除其他选中状态
        const agentList = document.getElementById('agentList');
        if (agentList) {
            agentList.querySelectorAll('.agent-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 选中当前Agent
        if (agentItem) {
            agentItem.classList.add('selected');
        }

        this.selectedAgent = agent;
        console.log('🤖 Agent选择已更新:', agent.agentName);

        // 更新Agent状态显示
        this.updateAgentStatus(agent);

        // 重置页面绑定状态（因为不同Agent的绑定配置不同）
        console.log('🔄 因Agent更换，重置页面绑定状态');
        this.resetPageBindingState();

        // 保存选择状态
        this.saveAgentSelection();

        console.log('✅ 选中Agent:', agent.agentName);
        this.showStatus('success', `已选择Agent: ${agent.agentName}`);
    }

    // 更新Agent状态显示
    updateAgentStatus(agent) {
        const currentAgentDisplay = document.getElementById('currentAgentDisplay');
        const currentAgentName = document.getElementById('currentAgentName');
        const agentSelectionArea = document.getElementById('agentSelectionArea');

        if (agent) {
            // 显示当前选择的Agent
            if (currentAgentName) {
                currentAgentName.textContent = agent.agentName;
            }
            if (currentAgentDisplay) {
                currentAgentDisplay.style.display = 'block';
            }

            // 隐藏Agent选择区域
            if (agentSelectionArea) {
                agentSelectionArea.classList.add('hidden');
            }

            // 绑定按钮事件
            this.bindAgentActionButtons();
        } else {
            // 隐藏当前Agent显示
            if (currentAgentDisplay) {
                currentAgentDisplay.style.display = 'none';
            }

            // 显示Agent选择区域
            if (agentSelectionArea) {
                agentSelectionArea.classList.remove('hidden');
            }
        }
    }

    // 绑定Agent操作按钮事件
    bindAgentActionButtons() {
        const changeBtn = document.getElementById('changeAgentBtn');

        if (changeBtn) {
            changeBtn.onclick = () => this.showAgentSelection();
        }
    }

    // 显示Agent选择区域
    showAgentSelection() {
        const currentAgentDisplay = document.getElementById('currentAgentDisplay');
        const agentSelectionArea = document.getElementById('agentSelectionArea');

        if (currentAgentDisplay) {
            currentAgentDisplay.style.display = 'none';
        }

        if (agentSelectionArea) {
            agentSelectionArea.classList.remove('hidden');
        }
    }

    // 清除Agent选择
    clearAgentSelection() {
        this.selectedAgent = null;

        // 移除所有选中状态
        const agentList = document.getElementById('agentList');
        if (agentList) {
            agentList.querySelectorAll('.agent-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 隐藏Agent状态
        this.updateAgentStatus(null);

        // 重置页面绑定状态
        this.resetPageBindingState();

        // 清除存储的选择状态
        this.saveAgentSelection();

        this.showStatus('info', '已清除Agent选择', 3000);
    }

    // 重置页面绑定状态
    resetPageBindingState() {
        console.log('🔄 重置页面绑定状态...');

        // 清除当前绑定
        this.currentBinding = null;

        // 隐藏绑定列表
        this.hideBindingList();

        // 重置绑定状态显示
        this.updateBindingStatus(null);

        console.log('✅ 页面绑定状态已重置');
    }

    // 调试方法：检查当前状态
    debugCurrentState() {
        console.log('🔍 当前状态检查:');
        console.log('  - selectedAgent:', this.selectedAgent);
        console.log('  - currentBinding:', this.currentBinding);
        console.log('  - authToken:', this.authToken ? '已设置' : '未设置');
    }

    // 截图识别
    async captureScreen() {
        try {
            if (!this.selectedAgent) {
                this.showStatus('error', '请先选择一个Agent');
                return;
            }

            // 记录开始时间
            const startTime = Date.now();

            // 显示加载动画
            this.showLoading('正在截图识别...');

            // 发送消息给Content Script执行截图
            const result = await this.sendMessageToContent({
                action: 'captureScreen',
                agent: this.selectedAgent
            });

            if (result && result.success) {
                // 计算耗时
                const duration = Date.now() - startTime;

                this.lastRecognitionResult = result.data;
                this.lastRecognitionRecordId = result.data.id; // 保存识别记录ID
                this.setCurrentRecognitionType('screenshot');
                this.showStatus('success', '截图完成');

                // 显示带耗时的状态
                const statusText = `截图完成`;
                const timingText = `耗时: ${(duration / 1000).toFixed(1)}秒`;
                this.updateRecognitionStatus(statusText, 'screenshot', timingText);

                // 保存识别结果到本地存储
                await this.saveRecognitionData('screenshot');
            } else {
                this.showStatus('error', result?.message || '截图识别失败');
            }
        } catch (error) {
            console.error('截图识别失败:', error);
            this.showStatus('error', `截图识别失败: ${error.message}`);
        } finally {
            // 隐藏加载动画
            this.hideLoading();
        }
    }

    // 上传文件
    uploadFile() {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    // 处理文件选择
    async handleFileSelect(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        const file = files[0];

        if (!this.selectedAgent) {
            this.showStatus('error', '请先选择一个Agent');
            return;
        }

        try {
            // 记录开始时间和文件信息
            const startTime = Date.now();
            const fileName = file.name;
            const fileSize = (file.size / 1024).toFixed(1); // KB

            // 显示加载动画
            this.showLoading(`正在识别 "${fileName}"...`);

            // 使用同步识别接口（与后台管理系统一致）
            const formData = new FormData();
            formData.append('file', file);
            formData.append('agentCode', this.selectedAgent.agentCode);

            const recognitionResponse = await fetch(`${this.serverUrl}/api/v1/recognition/analyzeByCode`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: formData
            });

            const recognitionResult = await recognitionResponse.json();

            if (recognitionResult.code === 200) {
                // 计算耗时
                const duration = Date.now() - startTime;

                // 提取并解析result字段中的JSON字符串
                let parsedResult = recognitionResult.data;
                if (parsedResult && parsedResult.result && typeof parsedResult.result === 'string') {
                    try {
                        // 解析JSON字符串，去掉转义
                        parsedResult.result = JSON.parse(parsedResult.result);
                    } catch (parseError) {
                        console.warn('解析result JSON失败，保持原始字符串:', parseError);
                    }
                }

                this.lastRecognitionResult = parsedResult;
                this.lastRecognitionRecordId = parsedResult.id; // 保存识别记录ID
                this.setCurrentRecognitionType('file');
                this.showStatus('success', '文件识别完成');

                // 显示带文件名和耗时的状态
                const statusText = `${fileName} (${fileSize}KB)`;
                const timingText = `耗时: ${(duration / 1000).toFixed(1)}秒`;
                this.updateRecognitionStatus(statusText, 'file', timingText);

                // 保存识别结果到本地存储
                await this.saveRecognitionData('file');
            } else {
                throw new Error(recognitionResult.message || '识别失败');
            }
        } catch (error) {
            console.error('文件识别失败:', error);
            this.showStatus('error', `文件识别失败: ${error.message}`);
        } finally {
            // 隐藏加载动画
            this.hideLoading();
            // 清空文件输入
            event.target.value = '';
        }
    }

    // 显示JSON模态框
    showJsonModal() {
        const modal = document.getElementById('jsonModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    // 隐藏JSON模态框
    hideJsonModal() {
        const modal = document.getElementById('jsonModal');
        if (modal) {
            modal.style.display = 'none';
        }

        // 清空输入
        const jsonInput = document.getElementById('jsonInput');
        if (jsonInput) {
            jsonInput.value = '';
        }
    }

    // 提交JSON数据
    async submitJsonData() {
        const jsonInput = document.getElementById('jsonInput');
        if (!jsonInput) return;

        const jsonText = jsonInput.value.trim();
        if (!jsonText) {
            this.showStatus('error', '请输入JSON数据');
            return;
        }

        try {
            // 验证JSON格式
            const jsonData = JSON.parse(jsonText);

            // 保存JSON数据（单独保存，不覆盖识别结果）
            this.lastJsonData = jsonData;
            this.setCurrentRecognitionType('json');
            this.hideJsonModal();

            // 更新识别状态显示
            this.updateRecognitionStatus('JSON数据已上传', 'json');

            // 保存到本地存储
            this.saveRecognitionData('json');

            this.showStatus('success', 'JSON数据已加载，可用于自动回填');

            console.log('✅ JSON数据已保存:', jsonData);
        } catch (error) {
            this.showStatus('error', 'JSON格式错误，请检查');
        }
    }

    // 测试与content script的连接
    async testConnection() {
        try {
            console.log('🔍 测试与content script的连接...');
            const response = await this.sendMessageToContent({ action: 'ping' });
            console.log('✅ 连接测试成功:', response);
            return true;
        } catch (error) {
            console.error('❌ 连接测试失败:', error);
            return false;
        }
    }

    // 验证当前页面绑定是否仍然有效
    async validateCurrentPageBinding() {
        try {
            if (!this.selectedAgent || !this.currentBinding) {
                return false;
            }

            console.log('🔍 验证当前页面绑定是否有效...');

            // 获取当前页面的URL
            const result = await this.sendMessageToContent({
                action: 'getCurrentPageInfo'
            });

            if (!result || !result.success) {
                console.log('❌ 无法获取当前页面信息');
                return false;
            }

            const currentUrl = result.data.url;
            const bindingUrlPattern = this.currentBinding.urlPattern;

            // 检查URL是否匹配绑定的URL模式
            const isUrlMatch = this.isUrlMatching(currentUrl, bindingUrlPattern);

            if (!isUrlMatch) {
                console.log('❌ 当前页面URL不匹配绑定配置:', {
                    currentUrl,
                    bindingUrlPattern
                });
                return false;
            }

            console.log('✅ 当前页面绑定验证通过');
            return true;
        } catch (error) {
            console.error('验证页面绑定失败:', error);
            return false;
        }
    }

    // URL匹配检查
    isUrlMatching(currentUrl, urlPattern) {
        try {
            // 如果没有URL模式，认为匹配
            if (!urlPattern) {
                return true;
            }

            // 简单的通配符匹配
            const pattern = urlPattern.replace(/\*/g, '.*');
            const regex = new RegExp(pattern, 'i');
            return regex.test(currentUrl);
        } catch (error) {
            console.error('URL匹配检查失败:', error);
            return false;
        }
    }

    // 检查页面绑定
    async checkPageBinding() {
        try {
            console.log('🔍 开始检查页面绑定，当前选择的Agent:', this.selectedAgent);

            // 首先检查是否选择了Agent
            if (!this.selectedAgent) {
                console.log('❌ 未选择Agent，停止检查页面绑定');
                this.showStatus('warning', '请先选择Agent，然后再选择目标页面');
                this.hideBindingList();
                this.updateBindingStatus(null);
                return;
            }

            console.log('✅ Agent已选择，继续检查页面绑定:', this.selectedAgent.agentName);

            // 先测试连接
            this.showStatus('info', '正在连接页面...');
            const connectionOk = await this.testConnection();
            if (!connectionOk) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
                return;
            }

            this.showStatus('info', '正在检查页面绑定...');

            const result = await this.sendMessageToContent({
                action: 'checkPageBinding',
                agent: this.selectedAgent
            });

            if (result && result.success) {
                const bindings = result.data;

                if (!bindings || bindings.length === 0) {
                    this.showStatus('warning', `当前页面没有找到与Agent "${this.selectedAgent.agentName}" 匹配的绑定配置`);
                    this.hideBindingList();
                    this.updateBindingStatus(null);
                    return;
                }

                // 显示绑定列表供用户选择
                this.showBindingList(bindings);
                this.showStatus('success', `找到 ${bindings.length} 个匹配的页面绑定`);
            } else {
                this.showStatus('error', result?.message || '页面绑定检查失败');
                this.hideBindingList();
                this.updateBindingStatus(null);
            }
        } catch (error) {
            console.error('页面绑定检查失败:', error);

            // 检查是否是页面连接问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('tab is not defined') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
            } else {
                this.showStatus('error', `页面绑定检查失败: ${error.message}`);
            }

            this.hideBindingList();
            this.updateBindingStatus(null);
        }
    }

    // 显示绑定列表
    showBindingList(bindings) {
        const bindingListContainer = document.getElementById('bindingListContainer');
        const bindingList = document.getElementById('bindingList');

        if (!bindingList || !bindingListContainer) return;

        // 清空列表
        bindingList.innerHTML = '';

        if (bindings.length === 0) {
            // 显示空状态
            bindingList.innerHTML = `
                <div class="binding-list-empty">
                    <div style="margin-bottom: 8px;">📋</div>
                    <div>当前页面没有匹配的绑定配置</div>
                </div>
            `;
        } else {
            // 渲染绑定项
            bindings.forEach((binding, index) => {
                const bindingItem = document.createElement('div');
                bindingItem.className = 'binding-item';
                bindingItem.setAttribute('data-binding-id', binding.id || index);

                // 构建描述信息
                let description = '';
                if (binding.templateName) {
                    description = `关联模板: ${binding.templateName}`;
                } else if (binding.urlPattern) {
                    description = `URL模式: ${binding.urlPattern}`;
                } else {
                    description = '无描述信息';
                }

                // 添加步骤信息
                let stepInfo = '';
                if (binding.isMultiStep === 1) {
                    const stepCount = (binding.subSteps?.length || 0) + 1;
                    stepInfo = ` <span class="badge badge-info">${stepCount}步骤</span>`;
                } else {
                    stepInfo = ' <span class="badge badge-secondary">单步骤</span>';
                }

                bindingItem.innerHTML = `
                    <div class="binding-name">${binding.bindingName || '未命名绑定'}${stepInfo}</div>
                    <div class="binding-description">${description}</div>
                `;

                // 添加点击事件
                bindingItem.addEventListener('click', () => {
                    this.selectBinding(binding, bindingItem);
                });



                bindingList.appendChild(bindingItem);
            });
        }

        // 显示容器
        bindingListContainer.style.display = 'block';
    }

    // 隐藏绑定列表
    hideBindingList() {
        const bindingListContainer = document.getElementById('bindingListContainer');
        if (bindingListContainer) {
            bindingListContainer.style.display = 'none';
        }
    }

    // 选择绑定
    selectBinding(binding, bindingItem) {
        // 移除其他选中状态
        const bindingList = document.getElementById('bindingList');
        if (bindingList) {
            bindingList.querySelectorAll('.binding-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // 选中当前绑定
        if (bindingItem) {
            bindingItem.classList.add('selected');
        }

        // 更新绑定状态
        const statusText = `已选择: ${binding.bindingName || '页面绑定'}`;
        console.log('📌 准备更新绑定状态:', statusText);
        this.updateBindingStatus(statusText, binding);

        // 隐藏绑定列表
        this.hideBindingList();

        console.log('✅ 选中绑定:', binding.bindingName);
        this.showStatus('success', `已选择绑定: ${binding.bindingName}`);
    }

    // 自动填充表单
    async autoFillForm() {
        try {
            // 检查是否选择了Agent
            if (!this.selectedAgent) {
                this.showStatus('error', '请先选择Agent');
                return;
            }

            // 检查是否选择了页面绑定
            if (!this.currentBinding) {
                this.showStatus('error', '请先选择目标页面');
                return;
            }

            // 检查当前页面是否仍然支持选定的Agent绑定
            const isPageSupported = await this.validateCurrentPageBinding();
            if (!isPageSupported) {
                this.showStatus('warning', `当前Agent "${this.selectedAgent.agentName}" 不支持此页面，请重新选择页面绑定或切换到支持的页面`);
                // 清除当前绑定状态，提示用户重新选择
                this.updateBindingStatus(null);
                return;
            }

            // 检查是否有数据可用于填充（文档识别结果或JSON数据）
            let fillData = null;
            let dataSource = '';

            if (this.lastRecognitionResult) {
                fillData = this.lastRecognitionResult;
                dataSource = '文档识别数据';
            } else if (this.lastJsonData) {
                fillData = this.lastJsonData;
                dataSource = 'JSON数据';
            }

            if (!fillData) {
                this.showStatus('error', '请先进行文档识别或上传JSON数据');
                return;
            }

            // 判断是否为多步骤绑定
            const isMultiStep = this.currentBinding.isMultiStep === 1;
            const totalSteps = isMultiStep ? (this.currentBinding.subSteps?.length || 0) + 1 : 1;

            // 显示加载动画
            if (isMultiStep) {
                this.showLoading(`正在执行多步骤表单填充...`, true, totalSteps);
            } else {
                this.showLoading(`正在使用${dataSource}自动填充表单...`);
            }

            try {
                const result = await this.sendMessageToContent({
                    action: 'autoFillForm',
                    data: fillData,
                    agent: this.selectedAgent,
                    binding: this.currentBinding
                });

                this.hideLoading();

                if (result && result.success) {
                    // 异步创建回填记录，不阻塞用户操作
                    this.createFillRecordAsync(fillData, result, dataSource);

                    this.displayFillResults(result, dataSource);
                    this.showStatus('success', `表单填充完成（使用${dataSource}）`);
                } else {
                    // 异步创建失败的回填记录，不阻塞用户操作
                    this.createFillRecordAsync(fillData, result, dataSource, false);

                    this.showStatus('error', result?.message || '表单填充失败');
                }
            } catch (fillError) {
                this.hideLoading();
                throw fillError;
            }
        } catch (error) {
            console.error('表单填充失败:', error);

            // 检查是否是页面连接问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('tab is not defined') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
            } else {
                this.showStatus('error', `表单填充失败: ${error.message}`);
            }
        }
    }

    // 显示详细的填充结果
    displayFillResults(result, dataSource) {
        const fillResult = document.getElementById('fillResult');
        if (!fillResult) return;

        const data = result.data;
        const stats = data.statistics;

        // 创建结果摘要
        const summaryHtml = `
            <div class="fill-result-summary">
                <div class="result-header">
                    <h6>📊 填充结果摘要</h6>
                    <small class="text-muted">数据源: ${dataSource}</small>
                </div>
                <div class="result-stats">
                    <div class="stat-item success">
                        <span class="stat-number">${stats.success}</span>
                        <span class="stat-label">成功</span>
                    </div>
                    <div class="stat-item failed">
                        <span class="stat-number">${stats.failed}</span>
                        <span class="stat-label">失败</span>
                    </div>
                    <div class="stat-item skipped">
                        <span class="stat-number">${stats.skipped}</span>
                        <span class="stat-label">跳过</span>
                    </div>
                    <div class="stat-item total">
                        <span class="stat-number">${data.totalFields}</span>
                        <span class="stat-label">总计</span>
                    </div>
                </div>
                <div class="result-meta">
                    <small>耗时: ${stats.totalDuration}ms | 平均: ${stats.averageDuration}ms/字段</small>
                </div>
                <div class="rating-section">
                    <div class="rating-title">对本次回填结果评价</div>
                    <div class="rating-buttons">
                        <button class="rating-btn thumbs-up" id="thumbsUpBtn" title="满意">👍</button>
                        <button class="rating-btn thumbs-down" id="thumbsDownBtn" title="不满意">👎</button>
                    </div>
                </div>
            </div>
        `;

        fillResult.innerHTML = summaryHtml;
        fillResult.style.display = 'block';

        // 保存详细结果用于后续使用
        this.lastFillResults = result;

        // 绑定点赞按钮事件
        this.bindRatingButtons();
    }

    // 绑定点赞按钮事件
    bindRatingButtons() {
        const thumbsUpBtn = document.getElementById('thumbsUpBtn');
        const thumbsDownBtn = document.getElementById('thumbsDownBtn');

        if (thumbsUpBtn) {
            thumbsUpBtn.onclick = () => this.handleRating('up');
        }

        if (thumbsDownBtn) {
            thumbsDownBtn.onclick = () => this.handleRating('down');
        }
    }

    // 处理点赞评价
    async handleRating(type) {
        const thumbsUpBtn = document.getElementById('thumbsUpBtn');
        const thumbsDownBtn = document.getElementById('thumbsDownBtn');

        // 清除之前的激活状态
        if (thumbsUpBtn) thumbsUpBtn.classList.remove('active');
        if (thumbsDownBtn) thumbsDownBtn.classList.remove('active');

        // 设置当前按钮为激活状态
        if (type === 'up' && thumbsUpBtn) {
            thumbsUpBtn.classList.add('active');
            this.showStatus('success', '感谢您的好评！', 2000);
        } else if (type === 'down' && thumbsDownBtn) {
            thumbsDownBtn.classList.add('active');
            this.showStatus('info', '感谢您的反馈，我们会持续改进！', 2000);
        }

        // 发送评价到后台
        console.log(`用户评价: ${type === 'up' ? '满意' : '不满意'}`);

        // 异步保存用户反馈到服务器，不阻塞用户操作
        if (this.currentFillRecordId) {
            this.saveUserFeedbackAsync(this.currentFillRecordId, type === 'up' ? 1 : 0);
        }
    }

    // 显示详细结果（侧边栏友好版本）
    showDetailedResults() {
        if (!this.lastFillResults) return;

        const data = this.lastFillResults.data;
        const stats = data.statistics;

        // 创建紧凑的详细结果HTML
        let detailsHtml = `
            <div class="compact-details-overlay" id="compactDetailsOverlay">
                <div class="compact-details-container">
                    <div class="compact-header">
                        <h6>📋 填充详情</h6>
                        <button class="compact-close" id="compactCloseBtn">×</button>
                    </div>

                    <div class="compact-body">
                        <!-- 快速统计 -->
                        <div class="quick-stats">
                            <div class="quick-stat success">
                                <span class="number">${stats.success}</span>
                                <span class="label">成功</span>
                            </div>
                            <div class="quick-stat failed">
                                <span class="number">${stats.failed}</span>
                                <span class="label">失败</span>
                            </div>
                            <div class="quick-stat skipped">
                                <span class="number">${stats.skipped}</span>
                                <span class="label">跳过</span>
                            </div>
                        </div>

                        <!-- 简化的Tab切换 -->
                        <div class="compact-tabs">
                            <button class="compact-tab active" data-tab="all">全部 (${data.totalFields})</button>
                            <button class="compact-tab" data-tab="failed">失败 (${stats.failed})</button>
                        </div>

                        <!-- 结果列表 -->
                        <div class="compact-results">
                            <div class="compact-tab-content active" id="compact-all">
                                ${this.generateCompactResultList(data.results)}
                            </div>
                            <div class="compact-tab-content" id="compact-failed">
                                ${this.generateCompactResultList(data.failedResults)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的详情面板
        const existingOverlay = document.getElementById('compactDetailsOverlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', detailsHtml);

        // 绑定事件
        this.initCompactDetailsEvents();
    }

    // 初始化紧凑详情面板事件
    initCompactDetailsEvents() {
        const overlay = document.getElementById('compactDetailsOverlay');
        if (!overlay) return;

        // 关闭按钮
        const closeBtn = overlay.querySelector('#compactCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                overlay.remove();
            });
        }

        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });

        // Tab切换
        const tabs = overlay.querySelectorAll('.compact-tab');
        const contents = overlay.querySelectorAll('.compact-tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active状态
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // 添加当前active状态
                tab.classList.add('active');
                const targetTab = tab.getAttribute('data-tab');
                const targetContent = overlay.querySelector(`#compact-${targetTab}`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
    }

    // 显示表单数据模态框
    showFormDataModal(formData) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="form-data-overlay" id="formDataOverlay">
                <div class="form-data-container">
                    <div class="form-data-header">
                        <h6>📊 表单数据详情</h6>
                        <button class="form-data-close" id="formDataCloseBtn">&times;</button>
                    </div>
                    <div class="form-data-body">
                        <div class="form-data-stats">
                            <div class="stat-item">
                                <span class="stat-number">${formData.totalFields || 0}</span>
                                <span class="stat-label">总字段数</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">${formData.filledFields || 0}</span>
                                <span class="stat-label">已填充</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">${formData.emptyFields || 0}</span>
                                <span class="stat-label">空字段</span>
                            </div>
                        </div>

                        <div class="form-data-tabs">
                            <div class="form-data-tab active" data-tab="all">全部 (${formData.totalFields || 0})</div>
                            <div class="form-data-tab" data-tab="filled">已填充 (${formData.filledFields || 0})</div>
                            <div class="form-data-tab" data-tab="empty">空字段 (${formData.emptyFields || 0})</div>
                        </div>

                        <div class="form-data-content">
                            <div class="form-data-tab-content active" id="form-data-all">
                                ${this.generateFormDataList(formData.fields, 'all')}
                            </div>
                            <div class="form-data-tab-content" id="form-data-filled">
                                ${this.generateFormDataList(formData.fields, 'filled')}
                            </div>
                            <div class="form-data-tab-content" id="form-data-empty">
                                ${this.generateFormDataList(formData.fields, 'empty')}
                            </div>
                        </div>

                        <div class="form-data-actions">
                            <button class="form-data-btn secondary" id="copyFormDataBtn">📋 复制数据</button>
                            <button class="form-data-btn secondary" id="exportFormDataBtn">💾 导出JSON</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定事件
        this.initFormDataModalEvents(formData);
    }

    // 生成表单数据列表
    generateFormDataList(fields, filter = 'all') {
        if (!fields || fields.length === 0) {
            return '<div class="form-data-empty">暂无数据</div>';
        }

        let filteredFields = fields;
        if (filter === 'filled') {
            filteredFields = fields.filter(field => field.value && field.value.trim() !== '');
        } else if (filter === 'empty') {
            filteredFields = fields.filter(field => !field.value || field.value.trim() === '');
        }

        if (filteredFields.length === 0) {
            return '<div class="form-data-empty">暂无数据</div>';
        }

        let listHtml = '<div class="form-data-list">';

        filteredFields.forEach((field, index) => {
            const hasValue = field.value && field.value.trim() !== '';
            const statusClass = hasValue ? 'filled' : 'empty';
            const statusIcon = hasValue ? '✅' : '⭕';
            const displayValue = field.value || '(空)';
            const fieldType = field.type || 'unknown';

            listHtml += `
                <div class="form-data-item ${statusClass}">
                    <div class="form-data-item-header">
                        <span class="status-icon">${statusIcon}</span>
                        <span class="field-name" title="${field.name || field.id || '未知字段'}">${(field.name || field.id || '未知字段').substring(0, 25)}${(field.name || field.id || '').length > 25 ? '...' : ''}</span>
                        <span class="field-type">${fieldType}</span>
                    </div>
                    <div class="form-data-item-body">
                        <div class="field-value" title="${displayValue}">${displayValue.length > 50 ? displayValue.substring(0, 50) + '...' : displayValue}</div>
                        <div class="field-meta">
                            <span class="field-id">ID: ${field.id || '无'}</span>
                            ${field.selector ? `<span class="field-selector">选择器: ${field.selector.substring(0, 20)}${field.selector.length > 20 ? '...' : ''}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        listHtml += '</div>';
        return listHtml;
    }

    // 初始化表单数据模态框事件
    initFormDataModalEvents(formData) {
        const overlay = document.getElementById('formDataOverlay');
        if (!overlay) return;

        // 关闭按钮
        const closeBtn = overlay.querySelector('#formDataCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                overlay.remove();
            });
        }

        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });

        // Tab切换
        const tabs = overlay.querySelectorAll('.form-data-tab');
        const contents = overlay.querySelectorAll('.form-data-tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active状态
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // 添加当前active状态
                tab.classList.add('active');
                const targetTab = tab.getAttribute('data-tab');
                const targetContent = overlay.querySelector(`#form-data-${targetTab}`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });

        // 复制数据按钮
        const copyBtn = overlay.querySelector('#copyFormDataBtn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                const dataText = this.formatFormDataForCopy(formData);
                navigator.clipboard.writeText(dataText).then(() => {
                    this.showStatus('success', '表单数据已复制到剪贴板', 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    this.showStatus('error', '复制失败');
                });
            });
        }

        // 导出JSON按钮
        const exportBtn = overlay.querySelector('#exportFormDataBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportFormDataAsJSON(formData);
            });
        }
    }

    // 格式化表单数据用于复制
    formatFormDataForCopy(formData) {
        let text = `=== 表单数据详情 ===\n`;
        text += `总字段数: ${formData.totalFields || 0}\n`;
        text += `已填充: ${formData.filledFields || 0}\n`;
        text += `空字段: ${formData.emptyFields || 0}\n\n`;

        text += `=== 字段详情 ===\n`;
        if (formData.fields && formData.fields.length > 0) {
            formData.fields.forEach((field, index) => {
                const hasValue = field.value && field.value.trim() !== '';
                const status = hasValue ? '[已填充]' : '[空]';
                text += `${index + 1}. ${status} ${field.name || field.id || '未知字段'}\n`;
                text += `   类型: ${field.type || 'unknown'}\n`;
                text += `   ID: ${field.id || '无'}\n`;
                text += `   值: ${field.value || '(空)'}\n`;
                if (field.selector) {
                    text += `   选择器: ${field.selector}\n`;
                }
                text += '\n';
            });
        }

        return text;
    }

    // 导出表单数据为JSON
    exportFormDataAsJSON(formData) {
        const jsonData = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            summary: {
                totalFields: formData.totalFields || 0,
                filledFields: formData.filledFields || 0,
                emptyFields: formData.emptyFields || 0
            },
            fields: formData.fields || []
        };

        const dataStr = JSON.stringify(jsonData, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `form-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showStatus('success', 'JSON文件已下载', 2000);
    }

    // 生成紧凑的结果列表
    generateCompactResultList(results) {
        if (!results || results.length === 0) {
            return '<div class="compact-empty">暂无数据</div>';
        }

        let listHtml = '<div class="compact-list">';

        results.forEach((result, index) => {
            const statusIcon = result.success ? '✅' : '❌';
            const statusClass = result.success ? 'success' : 'failed';
            const fieldName = result.field || '未知字段';
            const value = result.value ? String(result.value).substring(0, 15) : '-';
            const reason = result.reason || '';
            const controlType = result.controlType || 'unknown';

            listHtml += `
                <div class="compact-item ${statusClass}">
                    <div class="compact-item-header">
                        <span class="status-icon">${statusIcon}</span>
                        <span class="field-name" title="${fieldName}">${fieldName.length > 20 ? fieldName.substring(0, 20) + '...' : fieldName}</span>
                    </div>
                    <div class="compact-item-body">
                        <div class="item-value" title="${result.value || ''}">${value}${value.length === 15 && result.value && result.value.length > 15 ? '...' : ''}</div>
                        <div class="item-meta">
                            <span class="control-type">${controlType}</span>
                            ${!result.success ? `<span class="error-reason" title="${reason}">${reason.substring(0, 10)}${reason.length > 10 ? '...' : ''}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        listHtml += '</div>';
        return listHtml;
    }



    // 检测表单
    async detectForms() {
        try {
            this.showStatus('info', '正在检测页面表单...');

            const result = await this.sendMessageToContent({
                action: 'detectForms'
            });

            if (result && result.success) {
                this.showResultWithCloseButton('detectResult', this.formatFormDetectionResult(result.data));
                this.showStatus('success', '表单检测完成');
            } else {
                this.showStatus('error', result?.message || '表单检测失败');
            }
        } catch (error) {
            console.error('表单检测失败:', error);

            // 检查是否是页面连接问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('tab is not defined') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
            } else {
                this.showStatus('error', `表单检测失败: ${error.message}`);
            }
        }
    }

    // 提取数据（使用新的表单数据查看功能）
    async extractData() {
        try {
            this.showStatus('info', '正在提取表单数据...');

            const result = await this.sendMessageToContent({
                action: 'extractFormData'
            });

            if (result && result.success) {
                // 使用新的表单数据模态框显示
                this.showFormDataModal(result.data);
                this.showStatus('success', '数据提取完成');
            } else {
                this.showStatus('error', result?.message || '数据提取失败');
            }
        } catch (error) {
            console.error('数据提取失败:', error);

            // 检查是否是页面连接问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('tab is not defined') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
            } else {
                this.showStatus('error', `数据提取失败: ${error.message}`);
            }
        }
    }

    // 提取HTML
    async extractHTML() {
        try {
            this.showStatus('info', '正在复制页面HTML...');

            const result = await this.sendMessageToContent({
                action: 'extractHTML'
            });

            if (result && result.success) {
                // 假设复制总是成功的，因为在Chrome扩展环境下复制很少真正失败
                // 而且复制成功的检测在某些环境下不可靠
                this.showStatus('success', 'HTML已复制到剪贴板', 2000);
                if (!result.copySuccess) {
                    console.log('📋 提取的HTML数据:', result.data);
                    console.log('⚠️ 复制状态检测可能不准确，请检查剪贴板内容');
                }
            } else {
                this.showStatus('error', result?.message || 'HTML提取失败');
            }
        } catch (error) {
            console.error('HTML复制失败:', error);

            // 检查是否是页面连接问题
            if (error.message && (
                error.message.includes('Could not establish connection') ||
                error.message.includes('tab is not defined') ||
                error.message.includes('Cannot access') ||
                error.message.includes('页面连接异常')
            )) {
                this.showStatus('warning', '页面连接异常，请刷新当前页面后重试');
                this.showConnectionErrorModal();
            } else {
                this.showStatus('error', `HTML复制失败: ${error.message}`);
            }
        }
    }

    // 清除存储数据
    async clearStorage() {
        if (!confirm('确定要清除所有存储数据吗？这将清除登录状态、Agent选择、识别结果等所有数据。')) {
            return;
        }

        try {
            console.log('🧹 开始清除所有存储数据...');

            // 1. 清除Chrome存储
            await chrome.storage.local.clear();

            // 2. 重置所有内部状态
            this.authToken = '';
            this.userInfo = null;
            this.selectedAgent = null;
            this.userPermissions = [];
            this.lastRecognitionResult = null;
            this.lastJsonData = null;
            this.currentBinding = null;
            this.lastRecognitionRecordId = null;
            this.currentFillRecordId = null;

            // 3. 清除UI状态
            this.clearAllUIStates();

            // 4. 更新UI到初始状态
            this.updateUI();

            // 5. 重置服务器地址到默认值
            this.serverUrl = 'http://172.17.0.137:8080';
            const serverUrlInput = document.getElementById('serverUrl');
            if (serverUrlInput) {
                serverUrlInput.value = this.serverUrl;
            }

            this.showStatus('success', '所有存储数据已清除，页面已重置到初始状态');
            console.log('✅ 存储数据清除完成');

        } catch (error) {
            console.error('清除存储失败:', error);
            this.showStatus('error', `清除存储失败: ${error.message}`);
        }
    }

    // 清除所有UI状态
    clearAllUIStates() {
        console.log('🧹 清除所有UI状态...');

        // 清除Agent状态
        this.updateAgentStatus(null);

        // 清除绑定状态
        this.updateBindingStatus(null);

        // 清除识别结果显示
        this.clearRecognitionDisplay();

        // 清除JSON数据显示
        this.clearJsonDisplay();

        // 隐藏绑定列表
        this.hideBindingList();

        // 隐藏特定结果区域（但不清空innerHTML，避免破坏结构）
        const resultElements = [
            'recognitionResult', 'jsonDataStatus', 'fillResult'
        ];

        resultElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
            }
        });

        // 重置表单
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (form.id !== 'loginForm') { // 保留登录表单的服务器地址
                form.reset();
            }
        });

        // 清除文件输入
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.value = '';
        }

        console.log('✅ UI状态清除完成');
    }

    // 清除识别结果显示
    clearRecognitionDisplay() {
        const recognitionResult = document.getElementById('recognitionResult');
        if (recognitionResult) {
            recognitionResult.style.display = 'none';
            recognitionResult.innerHTML = '';
        }
    }

    // 清除JSON数据显示
    clearJsonDisplay() {
        const jsonDataStatus = document.getElementById('jsonDataStatus');
        if (jsonDataStatus) {
            jsonDataStatus.style.display = 'none';
            jsonDataStatus.innerHTML = '';
        }
    }



    // 隐藏所有功能按钮（未登录状态）
    hideAllFunctionButtons() {
        console.log('🔒 隐藏所有功能按钮（未登录状态）');

        // 隐藏需要登录的功能按钮（不包括设置区域的按钮）
        const buttonIds = [
            'captureBtn', 'uploadBtn', 'uploadJsonBtn',
            'checkBindingBtn', 'fillFormBtn', 'detectFormBtn', 'extractDataBtn',
            'htmlExtractBtn', 'loadAgentsBtn'
        ];

        buttonIds.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.style.display = 'none';
            }
        });

        // 隐藏整个功能区域（除了登录表单和设置区域）
        const featureSections = document.querySelectorAll('.feature-section');
        featureSections.forEach(section => {
            // 保留登录表单，隐藏其他功能区域
            if (section.id !== 'loginForm') {
                section.style.display = 'none';
            }
        });
    }

    // 显示所有功能区域（登录后）
    showAllFeatureSections() {
        console.log('🔓 显示所有功能区域（登录后）');

        // 显示所有功能区域
        const featureSections = document.querySelectorAll('.feature-section');
        featureSections.forEach(section => {
            if (section.id !== 'loginForm') {
                section.style.display = 'block';
            }
        });
    }

    // 显示带关闭按钮的结果
    showResultWithCloseButton(resultId, content) {
        const resultElement = document.getElementById(resultId);
        if (!resultElement) return;

        resultElement.style.display = 'block';
        resultElement.innerHTML = `
            <div class="result-header">
                <span class="result-title">结果</span>
                <button class="result-close-btn" onclick="this.parentElement.parentElement.style.display='none'">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="result-content">
                ${content}
            </div>
        `;
    }

    // 格式化表单检测结果
    formatFormDetectionResult(forms) {
        if (!forms || forms.length === 0) {
            return '<h4>未检测到表单</h4><p>当前页面没有可填充的表单元素</p>';
        }

        let html = '<h4>📋 表单检测结果</h4>';

        forms.forEach((form, index) => {
            if (form.isPageButtons) {
                // 显示按钮信息
                html += `<div style="margin: 10px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">`;
                html += `<strong>🔘 页面按钮 (${form.fields.length}个)</strong><br>`;
                form.fields.forEach(button => {
                    html += `<small>• ${button.text || button.type} (${button.tagName})</small><br>`;
                });
                html += `</div>`;
            } else {
                // 显示表单信息
                html += `<div style="margin: 10px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">`;
                html += `<strong>📝 表单 ${form.formIndex + 1}</strong>`;
                if (form.isInIframe) {
                    html += ` <small>(iframe中)</small>`;
                }
                html += `<br>`;
                html += `<small>字段数量: ${form.fields.length}</small><br>`;

                // 显示前几个字段
                const displayFields = form.fields.slice(0, 3);
                displayFields.forEach(field => {
                    const fieldName = field.placeholder || field.name || field.id || `${field.type}字段`;
                    html += `<small>• ${fieldName} (${field.tagName})</small><br>`;
                });

                if (form.fields.length > 3) {
                    html += `<small>... 还有 ${form.fields.length - 3} 个字段</small><br>`;
                }
                html += `</div>`;
            }
        });

        html += `<div style="margin-top: 10px; font-size: 11px; opacity: 0.8;">`;
        html += `💡 提示: 只显示可填充的表单元素，已过滤掉装饰性内容`;
        html += `</div>`;

        return html;
    }

    // 格式化数据提取结果
    formatExtractDataResult(data) {
        if (!data || Object.keys(data).length === 0) {
            return '<h4>未提取到数据</h4><p>当前页面没有可提取的表单数据</p>';
        }

        let html = '<h4>📤 提取的表单数据</h4>';

        const entries = Object.entries(data);
        const displayEntries = entries.slice(0, 10); // 只显示前10个

        html += `<div style="font-family: monospace; font-size: 11px;">`;
        displayEntries.forEach(([key, value]) => {
            const displayValue = typeof value === 'string' ?
                (value.length > 30 ? value.substring(0, 30) + '...' : value) :
                String(value);
            html += `<div style="margin: 4px 0; padding: 4px; background: rgba(255,255,255,0.05); border-radius: 2px;">`;
            html += `<strong>${key}:</strong> ${displayValue}`;
            html += `</div>`;
        });
        html += `</div>`;

        if (entries.length > 10) {
            html += `<div style="margin-top: 8px; font-size: 11px; opacity: 0.8;">`;
            html += `... 还有 ${entries.length - 10} 个字段`;
            html += `</div>`;
        }

        html += `<div style="margin-top: 10px; font-size: 11px; opacity: 0.8;">`;
        html += `💡 提示: 只显示可编辑表单元素的数据，共 ${entries.length} 个字段`;
        html += `</div>`;

        return html;
    }

    // ==================== 加载状态管理 ====================

    // 显示加载遮罩
    showLoading(message = '正在处理...', isMultiStep = false, totalSteps = 1) {
        this.loadingState = {
            isLoading: true,
            currentStep: 0,
            totalSteps: totalSteps,
            message: message
        };

        const overlay = document.getElementById('loadingOverlay');
        const loadingText = document.getElementById('loadingText');
        const loadingProgress = document.getElementById('loadingProgress');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (overlay) {
            overlay.style.display = 'flex';
        }

        if (loadingText) {
            loadingText.textContent = message;
        }

        if (isMultiStep && totalSteps > 1) {
            // 显示进度条
            if (loadingProgress) {
                loadingProgress.style.display = 'block';
            }
            if (progressFill) {
                progressFill.style.width = '0%';
            }
            if (progressText) {
                progressText.textContent = `第1步 / 共${totalSteps}步`;
            }
        } else {
            // 隐藏进度条
            if (loadingProgress) {
                loadingProgress.style.display = 'none';
            }
        }

        console.log('🔄 显示加载遮罩:', message);
    }

    // 更新加载进度
    updateLoadingProgress(currentStep, message = null) {
        this.loadingState.currentStep = currentStep;
        if (message) {
            this.loadingState.message = message;
        }

        const loadingText = document.getElementById('loadingText');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (loadingText && message) {
            loadingText.textContent = message;
        }

        if (this.loadingState.totalSteps > 1) {
            const progress = (currentStep / this.loadingState.totalSteps) * 100;

            if (progressFill) {
                progressFill.style.width = `${progress}%`;
            }

            if (progressText) {
                progressText.textContent = `第${currentStep}步 / 共${this.loadingState.totalSteps}步`;
            }
        }

        console.log(`📊 更新进度: ${currentStep}/${this.loadingState.totalSteps} - ${message || this.loadingState.message}`);
    }

    // 隐藏加载遮罩
    hideLoading() {
        this.loadingState.isLoading = false;

        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }

        console.log('✅ 隐藏加载遮罩');
    }

    // 等待元素出现
    async waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(new Error(`等待元素超时: ${selector}`));
                    return;
                }

                setTimeout(checkElement, 100);
            };

            checkElement();
        });
    }

    // 等待页面稳定（用于等待弹窗等动态内容加载完成）
    async waitForPageStable(timeout = 3000) {
        return new Promise(resolve => {
            let lastHeight = document.body.scrollHeight;
            let stableCount = 0;
            const requiredStableCount = 5; // 需要连续5次高度不变才认为稳定

            const checkStability = () => {
                const currentHeight = document.body.scrollHeight;
                if (currentHeight === lastHeight) {
                    stableCount++;
                    if (stableCount >= requiredStableCount) {
                        resolve();
                        return;
                    }
                } else {
                    stableCount = 0;
                    lastHeight = currentHeight;
                }

                setTimeout(checkStability, 200);
            };

            checkStability();

            // 超时保护
            setTimeout(() => {
                resolve();
            }, timeout);
        });
    }

    // ==================== 主题管理方法 ====================

    // 切换主题
    async switchTheme(theme) {
        if (this.currentTheme === theme) return;

        console.log(`🎨 切换主题: ${this.currentTheme} -> ${theme}`);

        this.currentTheme = theme;
        this.applyTheme(theme);

        // 切换主题后自动收起主题切换器
        this.themeSwitcherCollapsed = true;
        this.updateThemeSwitcherUI();

        // 保存主题设置和收起状态
        try {
            await chrome.storage.local.set({
                currentTheme: theme,
                themeSwitcherCollapsed: this.themeSwitcherCollapsed
            });
            console.log('✅ 主题设置已保存');
        } catch (error) {
            console.error('❌ 保存主题设置失败:', error);
        }
    }

    // 应用主题
    applyTheme(theme) {
        const body = document.body;

        // 移除所有主题类
        body.removeAttribute('data-theme');

        // 应用新主题
        if (theme !== 'default') {
            body.setAttribute('data-theme', theme);
        }

        console.log(`🎨 主题已应用: ${theme}`);
    }

    // 更新主题切换器UI
    updateThemeSwitcherUI() {
        // 更新主题选项的激活状态
        document.querySelectorAll('.theme-option').forEach(option => {
            const optionTheme = option.getAttribute('data-theme');
            if (optionTheme === this.currentTheme) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });

        // 更新主题切换器的收起状态
        const themeSwitcher = document.getElementById('themeSwitcher');
        if (themeSwitcher) {
            if (this.themeSwitcherCollapsed) {
                themeSwitcher.classList.add('collapsed');
            } else {
                themeSwitcher.classList.remove('collapsed');
            }
        }

        // 更新切换按钮的背景色以反映当前主题
        const toggleBtn = document.getElementById('themeSwitcherToggle');
        if (toggleBtn) {
            this.updateToggleButtonTheme(toggleBtn);
        }
    }

    // 更新切换按钮的主题色
    updateToggleButtonTheme(toggleBtn) {
        const themeColors = {
            'default': 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
            'light': 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
            'tech-blue': 'linear-gradient(135deg, #0c4a6e 0%, #075985 100%)',
            'business-green': 'linear-gradient(135deg, #064e3b 0%, #065f46 100%)',
            'purple-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        };

        const textColors = {
            'default': '#ffffff',
            'light': '#1e293b',
            'tech-blue': '#ffffff',
            'business-green': '#ffffff',
            'purple-gradient': '#ffffff'
        };

        const currentColor = themeColors[this.currentTheme] || themeColors['default'];
        const textColor = textColors[this.currentTheme] || textColors['default'];

        toggleBtn.style.background = currentColor;
        toggleBtn.style.color = textColor;
        toggleBtn.style.borderRadius = '6px';
        toggleBtn.style.border = this.currentTheme === 'light' ? '1px solid rgba(0, 0, 0, 0.2)' : '1px solid rgba(255, 255, 255, 0.2)';
    }

    // 切换主题切换器的展开/收起状态
    async toggleThemeSwitcher() {
        this.themeSwitcherCollapsed = !this.themeSwitcherCollapsed;
        this.updateThemeSwitcherUI();

        // 保存状态
        try {
            await chrome.storage.local.set({ themeSwitcherCollapsed: this.themeSwitcherCollapsed });
        } catch (error) {
            console.error('❌ 保存主题切换器状态失败:', error);
        }
    }

    // 异步创建回填记录（不阻塞用户操作）
    createFillRecordAsync(originalData, result, dataSource, isSuccess = true) {
        console.log('📝 开始异步保存回填记录...');
        // 使用setTimeout确保异步执行，不阻塞UI
        setTimeout(async () => {
            try {
                const record = await this.createFillRecord(originalData, result, dataSource, isSuccess);
                if (record) {
                    console.log('📝 回填记录保存成功（后台）:', record.id);
                }
            } catch (error) {
                // 静默处理错误，不影响用户体验
                console.warn('📝 回填记录保存失败（不影响用户操作）:', error);
            }
        }, 0);
    }

    // 创建回填记录（内部方法）
    async createFillRecord(originalData, result, dataSource, isSuccess = true) {
        try {
            if (!this.currentBinding || !this.selectedAgent) {
                console.warn('缺少必要信息，无法创建回填记录');
                return null;
            }

            // 获取当前页面URL
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentUrl = tabs[0]?.url || window.location.href;

            // 确定回填结果状态
            let fillResult = 3; // 默认失败
            let errorMessage = null;

            if (isSuccess && result && result.success) {
                const stats = result.data?.statistics;
                if (stats) {
                    if (stats.failed === 0 && stats.errors === 0) {
                        fillResult = 1; // 完全成功
                    } else if (stats.success > 0) {
                        fillResult = 2; // 部分成功
                    }
                }
            } else {
                errorMessage = result?.message || '表单填充失败';
            }

            // 构建实际填充的数据（从成功的结果中提取）
            let actualFillData = {};
            if (result && result.data && result.data.successResults) {
                // 从成功填充的字段中构建数据
                result.data.successResults.forEach(item => {
                    if (item.field && item.value !== undefined) {
                        actualFillData[item.field] = item.value;
                    }
                });
            }

            // 如果没有成功的填充数据，使用原始数据作为备份
            if (Object.keys(actualFillData).length === 0) {
                actualFillData = originalData || {};
            }

            // 构建请求数据
            const requestData = {
                recognitionRecordId: this.lastRecognitionRecordId || -1, // 如果没有识别记录ID，使用-1表示JSON数据
                pageBindingId: this.currentBinding.id,
                targetUrl: currentUrl,
                fillData: JSON.stringify(actualFillData),
                fillResult: fillResult,
                errorMessage: errorMessage
            };

            console.log('创建回填记录:', requestData);

            // 发送请求到后端
            const response = await this.apiCall(`${this.serverUrl}/api/v1/form-fill-records`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            const apiResult = await response.json();

            if (apiResult && apiResult.code === 200) {
                console.log('📝 回填记录API调用成功:', apiResult.data);
                this.currentFillRecordId = apiResult.data.id; // 保存记录ID用于后续反馈
                return apiResult.data;
            } else {
                console.warn('📝 创建回填记录API失败:', apiResult?.message);
                return null;
            }

        } catch (error) {
            // 使用warn级别日志，因为这不是关键错误
            console.warn('📝 创建回填记录失败（后台记录）:', error.message || error);
            return null;
        }
    }

    // 异步保存用户反馈（不阻塞用户操作）
    saveUserFeedbackAsync(recordId, userFeedback, comment = null) {
        console.log('👍 开始异步保存用户反馈...');
        // 使用setTimeout确保异步执行，不阻塞UI
        setTimeout(async () => {
            try {
                const success = await this.saveUserFeedback(recordId, userFeedback, comment);
                if (success) {
                    console.log('👍 用户反馈保存成功（后台）');
                }
            } catch (error) {
                // 静默处理错误，不影响用户体验
                console.warn('👍 用户反馈保存失败（不影响用户操作）:', error);
            }
        }, 0);
    }

    // 保存用户反馈（内部方法）
    async saveUserFeedback(recordId, userFeedback, comment = null) {
        try {
            const requestData = {
                recordId: recordId,
                userFeedback: userFeedback, // 1-点赞, 0-踩
                feedbackComment: comment
            };

            console.log('保存用户反馈:', requestData);

            const response = await this.apiCall(`${this.serverUrl}/api/v1/form-fill-records/feedback`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            const apiResult = await response.json();

            if (apiResult && apiResult.code === 200) {
                console.log('👍 用户反馈API调用成功:', apiResult.data);
                return true;
            } else {
                console.warn('👍 保存用户反馈API失败:', apiResult?.message);
                return false;
            }

        } catch (error) {
            // 使用warn级别日志，因为这不是关键错误
            console.warn('👍 保存用户反馈失败（后台记录）:', error.message || error);
            return false;
        }
    }


}



// 注意：页面绑定检查现在由 Background Script 处理，不再需要 Side Panel 的消息监听器

// 初始化Side Panel
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Side Panel DOM加载完成，初始化应用...');
    window.sinoairSidePanel = new SinoairSidePanel();
    console.log('✅ Side Panel实例已创建:', window.sinoairSidePanel);
});
