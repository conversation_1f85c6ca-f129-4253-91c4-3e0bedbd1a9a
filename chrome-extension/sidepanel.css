/* SinoairAgent Side Panel 样式 - 与后台管理系统主题同步 */

:root {
    /* 默认主题 - 企业级中等蓝色 */
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --sidebar-bg: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --bg-overlay: rgba(255, 255, 255, 0.1);
    /* 识别状态专用颜色 - 深色主题：浅绿色背景上的白色文字 */
    --recognition-text-primary: #ffffff;
    --recognition-text-secondary: rgba(255, 255, 255, 0.8);
    --bg-overlay-hover: rgba(255, 255, 255, 0.15);
    --bg-overlay-active: rgba(255, 255, 255, 0.2);

    /* 模态框变量 - 默认主题 */
    --modal-overlay-bg: rgba(0, 0, 0, 0.7);
    --modal-bg: #334155;
    --modal-text: #ffffff;
    --modal-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    --modal-border: rgba(255, 255, 255, 0.2);
    --modal-input-bg: #475569;
    --modal-placeholder: rgba(255, 255, 255, 0.6);
    --modal-secondary-btn: rgba(255, 255, 255, 0.1);
    --modal-secondary-btn-hover: rgba(255, 255, 255, 0.2);
    --modal-border: 1px solid rgba(255, 255, 255, 0.1);
    --modal-border-internal: 1px solid rgba(255, 255, 255, 0.08);
    --modal-header-bg: #475569;
    --modal-header-text: #ffffff;
    --modal-close-hover: rgba(255, 255, 255, 0.1);
    --modal-code-bg: rgba(0, 0, 0, 0.2);
    --modal-code-text: #e2e8f0;
    --modal-code-border: 1px solid rgba(255, 255, 255, 0.05);
    --modal-footer-bg: #475569;
    --modal-button-bg: #64748b;
    --modal-button-text: #ffffff;
    --modal-button-border: none;
}

/* 浅色主题 */
[data-theme="light"] {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --sidebar-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --bg-overlay: rgba(0, 0, 0, 0.05);
    /* 识别状态专用颜色 - 白色主题：浅绿色背景上的深色文字 */
    --recognition-text-primary: #1e293b;
    --recognition-text-secondary: #475569;
    --bg-overlay-hover: rgba(0, 0, 0, 0.1);
    --bg-overlay-active: rgba(0, 0, 0, 0.15);

    /* 模态框变量 - 浅色主题 */
    --modal-overlay-bg: rgba(0, 0, 0, 0.6);
    --modal-bg: #ffffff;
    --modal-text: #1e293b;
    --modal-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    --modal-border: #e2e8f0;
    --modal-input-bg: #f8fafc;
    --modal-placeholder: #64748b;
    --modal-secondary-btn: #f1f5f9;
    --modal-secondary-btn-hover: #e2e8f0;
    --modal-border: 1px solid #e2e8f0;
    --modal-border-internal: 1px solid #f1f5f9;
    --modal-header-bg: #f8fafc;
    --modal-header-text: #1e293b;
    --modal-close-hover: rgba(0, 0, 0, 0.05);
    --modal-code-bg: #f8fafc;
    --modal-code-text: #374151;
    --modal-code-border: 1px solid #e5e7eb;
    --modal-footer-bg: #f8fafc;
    --modal-button-bg: #e2e8f0;
    --modal-button-text: #374151;
    --modal-button-border: 1px solid #cbd5e1;
}

/* 浅色主题按钮优化 - 企业级中等蓝色 */
[data-theme="light"] .primary-button {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.25), var(--card-shadow);
}

[data-theme="light"] .primary-button:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.35), var(--card-shadow-lg);
}

[data-theme="light"] .secondary-button {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    color: var(--text-primary);
    border: 2px solid #cbd5e1;
}

[data-theme="light"] .secondary-button:hover {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    border-color: #94a3b8;
}

/* 默认主题按钮优化 - 企业级中等蓝色 */
[data-theme="default"] .primary-button, :root .primary-button {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3), var(--card-shadow);
}

[data-theme="default"] .primary-button:hover, :root .primary-button:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4), var(--card-shadow-lg);
}

[data-theme="default"] .secondary-button, :root .secondary-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.12));
    border: 2px solid rgba(255, 255, 255, 0.15);
}

[data-theme="default"] .secondary-button:hover, :root .secondary-button:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.18));
    border-color: rgba(255, 255, 255, 0.25);
}

/* 科技蓝主题 */
[data-theme="tech-blue"] {
    --primary-color: #0ea5e9;
    --primary-dark: #0284c7;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #0c4a6e;
    --light-color: #f0f9ff;
    --border-color: #7dd3fc;
    --sidebar-bg: linear-gradient(180deg, #0c4a6e 0%, #075985 100%);
    --card-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.2), 0 2px 4px -1px rgba(14, 165, 233, 0.1);
    --card-shadow-lg: 0 10px 15px -3px rgba(14, 165, 233, 0.2), 0 4px 6px -2px rgba(14, 165, 233, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --bg-overlay: rgba(255, 255, 255, 0.1);
    /* 识别状态专用颜色 - 蓝色主题：浅绿色背景上的白色文字 */
    --recognition-text-primary: #ffffff;
    --recognition-text-secondary: rgba(255, 255, 255, 0.8);
    --bg-overlay-hover: rgba(255, 255, 255, 0.15);
    --bg-overlay-active: rgba(255, 255, 255, 0.2);

    /* 模态框变量 - 科技蓝主题 */
    --modal-overlay-bg: rgba(0, 0, 0, 0.7);
    --modal-bg: #075985;
    --modal-text: #ffffff;
    --modal-shadow: 0 8px 25px rgba(14, 165, 233, 0.2);
    --modal-border: rgba(125, 211, 252, 0.3);
    --modal-input-bg: #0c4a6e;
    --modal-placeholder: rgba(255, 255, 255, 0.6);
    --modal-secondary-btn: rgba(125, 211, 252, 0.1);
    --modal-secondary-btn-hover: rgba(125, 211, 252, 0.2);
    --modal-border: 1px solid rgba(125, 211, 252, 0.2);
    --modal-border-internal: 1px solid rgba(125, 211, 252, 0.1);
    --modal-header-bg: #0c4a6e;
    --modal-header-text: #ffffff;
    --modal-close-hover: rgba(255, 255, 255, 0.1);
    --modal-code-bg: rgba(0, 0, 0, 0.2);
    --modal-code-text: #bae6fd;
    --modal-code-border: 1px solid rgba(125, 211, 252, 0.1);
    --modal-footer-bg: #0c4a6e;
    --modal-button-bg: #0891b2;
    --modal-button-text: #ffffff;
    --modal-button-border: none;
}

/* 科技蓝主题按钮优化 */
[data-theme="tech-blue"] .primary-button {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3), var(--card-shadow);
}

[data-theme="tech-blue"] .primary-button:hover {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4), var(--card-shadow-lg);
}

[data-theme="tech-blue"] .secondary-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.15));
    border: 2px solid rgba(125, 211, 252, 0.3);
}

[data-theme="tech-blue"] .secondary-button:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.22));
    border-color: rgba(125, 211, 252, 0.5);
}



* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html {
    background: var(--sidebar-bg);
    min-height: 100vh;
}

body {
    font-size: 14px;
    line-height: 1.4;
    color: var(--text-primary);
    background: var(--sidebar-bg);
    overflow-x: hidden;
    min-height: 100vh;
    transition: all 0.3s ease;
}

.sidepanel-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--sidebar-bg);
    color: var(--text-primary);
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    background: transparent;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 80px; /* 为主题切换器留出空间 */
}

/* 头部样式 */
.header {
    padding: 16px;
    background: var(--bg-overlay);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.logo img {
    width: 24px;
    height: 24px;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: var(--bg-overlay);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.status-dot.success {
    background: var(--success-color);
    box-shadow: 0 0 8px rgba(5, 150, 105, 0.5);
    animation: pulse 2s infinite;
}

.status-dot.error {
    background: var(--danger-color);
    box-shadow: 0 0 8px rgba(220, 38, 38, 0.5);
}

.status-dot.warning {
    background: var(--warning-color);
    box-shadow: 0 0 8px rgba(217, 119, 6, 0.5);
}

.status-text {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7); }
    70% { box-shadow: 0 0 0 6px rgba(5, 150, 105, 0); }
    100% { box-shadow: 0 0 0 0 rgba(5, 150, 105, 0); }
}

/* 企业级功能区样式 */
.feature-section {
    margin: 12px;
    padding: 16px;
    background: var(--bg-overlay);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.feature-section:hover {
    background: var(--bg-overlay-hover);
    border-color: rgba(255, 255, 255, 0.12);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.feature-section.hidden {
    display: none !important;
}

.feature-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 2px;
}

/* Agent搜索框样式 */
.agent-search-container {
    margin: 16px 0;
}

.agent-search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: var(--bg-overlay);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.agent-search-input::placeholder {
    color: var(--text-muted);
}

.agent-search-input:focus {
    border-color: var(--primary-color);
    background: var(--bg-overlay-hover);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 当前Agent显示样式 - 美化版本 */
.current-agent-display {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.2));
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15), var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.current-agent-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2px; /* 内缩2px，避免超出边框 */
    right: 2px; /* 内缩2px，避免超出边框 */
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #60a5fa, var(--primary-color));
    animation: shimmer 2s ease-in-out infinite;
    border-radius: 1px; /* 添加圆角，与容器风格一致 */
}

.current-agent-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.current-agent-label {
    font-size: 13px;
    color: var(--text-secondary);
    margin-right: 8px;
    font-weight: 500;
}

.current-agent-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.agent-actions {
    display: flex;
    justify-content: flex-end;
}

.change-agent-btn {
    flex: 1; /* 与重新选择按钮一样的宽度设置 */
    padding: 10px 16px; /* 与重新选择按钮一致的内边距 */
    border: none;
    border-radius: 8px;
    font-size: 13px; /* 与重新选择按钮一致的字体大小 */
    cursor: pointer;
    transition: all 0.3s ease; /* 与重新选择按钮一致的过渡效果 */
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-weight: 600;
    box-shadow: var(--card-shadow); /* 与重新选择按钮一致的阴影 */
    border: 1px solid rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.change-agent-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3), var(--card-shadow-lg);
}

.change-agent-btn:active {
    transform: translateY(0);
    box-shadow: var(--card-shadow);
}

/* Agent选择区域样式 */
.agent-selection-area {
    /* 默认显示 */
}

.agent-selection-area.hidden {
    display: none;
}

/* 识别状态显示样式 - 支持两行布局 */
.recognition-status {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(5, 150, 105, 0.2));
    border: 2px solid var(--success-color);
    border-radius: 12px;
    padding: 14px 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15), var(--card-shadow);
    position: relative;
    overflow: visible;
    min-height: 60px; /* 适应两行布局 */
    display: flex;
    align-items: center;
    gap: 12px;
}

.recognition-status p {
    margin: 0;
    color: white;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.4;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 识别状态信息区域 */
.recognition-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.recognition-main {
    display: flex;
    align-items: center; /* 居中对齐 */
    gap: 8px;
    width: 100%;
}

.recognition-timing {
    display: flex;
    justify-content: center;
    margin-top: 2px;
}

.timing-text {
    color: var(--recognition-text-secondary); /* 使用较浅的颜色 */
    font-size: 11px;
    font-weight: 400;
}

.recognition-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2px; /* 内缩2px，避免超出边框 */
    right: 2px; /* 内缩2px，避免超出边框 */
    height: 3px;
    background: linear-gradient(90deg, var(--success-color), #34d399, var(--success-color));
    animation: shimmer 2s ease-in-out infinite;
    border-radius: 1px; /* 添加圆角，与容器风格一致 */
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.recognition-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.recognition-label {
    font-size: 13px;
    color: var(--recognition-text-secondary); /* 使用较浅的颜色，类似current-agent-label */
    margin-right: 8px;
    font-weight: 500;
    white-space: nowrap; /* 标签不换行 */
    flex-shrink: 0; /* 标签不缩小 */
}

.recognition-text {
    font-size: 13px;
    font-weight: 600; /* 增加字重，类似current-agent-name */
    color: var(--recognition-text-primary); /* 使用主要颜色 */
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis; /* 使用省略号 */
    white-space: nowrap; /* 不换行 */
    max-width: 200px; /* 限制最大宽度 */
    cursor: help; /* 鼠标悬停时显示帮助光标 */
    line-height: 1.3;
}

.recognition-actions {
    display: flex;
    gap: 12px;
    justify-content: space-between; /* 左右分布 */
}

.view-result-btn, .re-recognize-btn {
    flex: 1; /* 平分宽度 */
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.view-result-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.view-result-btn:hover {
    background: linear-gradient(135deg, #059669, #10b981);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3), var(--card-shadow-lg);
}

.re-recognize-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: 1px solid rgba(37, 99, 235, 0.3);
}

.re-recognize-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3), var(--card-shadow-lg);
}

/* 按钮点击效果 */
.view-result-btn:active, .re-recognize-btn:active {
    transform: translateY(0);
    box-shadow: var(--card-shadow);
}

/* 保留的识别按钮样式 */
.active-recognition-button {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.active-recognition-button .primary-button,
.active-recognition-button .secondary-button {
    width: 100%;
    margin-bottom: 0;
}

/* 浅色主题下的保留按钮样式 */
[data-theme="light"] .active-recognition-button {
    border-bottom-color: rgba(0, 0, 0, 0.1);
}

/* 绑定状态显示样式 - 美化版本 */
.binding-status {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.2));
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15), var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.binding-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2px; /* 内缩2px，避免超出边框 */
    right: 2px; /* 内缩2px，避免超出边框 */
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #60a5fa, var(--primary-color));
    animation: shimmer 2s ease-in-out infinite;
    border-radius: 1px; /* 添加圆角，与容器风格一致 */
}

.binding-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.binding-label {
    font-size: 13px;
    color: var(--text-secondary);
    margin-right: 8px;
    font-weight: 500;
}

.binding-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.binding-actions {
    display: flex;
    justify-content: flex-end;
}

.change-binding-btn {
    flex: 1; /* 与重新选择按钮一样的宽度设置 */
    padding: 10px 16px; /* 与重新选择按钮一致的内边距 */
    border: none;
    border-radius: 8px;
    font-size: 13px; /* 与重新选择按钮一致的字体大小 */
    cursor: pointer;
    transition: all 0.3s ease; /* 与重新选择按钮一致的过渡效果 */
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-weight: 600;
    box-shadow: var(--card-shadow); /* 与重新选择按钮一致的阴影 */
    border: 1px solid rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.change-binding-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3), var(--card-shadow-lg);
}

.change-binding-btn:active {
    transform: translateY(0);
    box-shadow: var(--card-shadow);
}

/* 操作区域样式 */
.recognition-actions-area, .fill-actions-area {
    /* 默认显示 */
}

.recognition-actions-area.hidden, .fill-actions-area.hidden {
    display: none;
}

/* 绑定列表容器样式 */
.binding-list-container {
    margin: 16px 0;
    background: var(--bg-overlay);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
}

.binding-list {
    max-height: 250px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.binding-list::-webkit-scrollbar {
    width: 6px;
}

.binding-list::-webkit-scrollbar-track {
    background: transparent;
}

.binding-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.binding-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 绑定项样式 */
.binding-item {
    background: var(--bg-overlay);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
}

.binding-item:last-child {
    margin-bottom: 0;
}

.binding-item:hover {
    background: var(--bg-overlay-hover);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: var(--card-shadow-lg);
}

.binding-item.selected {
    background: rgba(37, 99, 235, 0.25);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.binding-item.selected:hover {
    background: rgba(37, 99, 235, 0.3);
    border-color: var(--primary-color);
}

/* 绑定项内容样式 */
.binding-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
    line-height: 1.3;
}

.binding-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.binding-item.selected .binding-name {
    color: #ffffff;
}

.binding-item.selected .binding-description {
    color: rgba(255, 255, 255, 0.9);
}

/* 空状态样式 */
.binding-list-empty {
    text-align: center;
    padding: 30px 20px;
    color: var(--text-muted);
    font-size: 14px;
    background: var(--bg-overlay);
    border-radius: 8px;
    border: 1px dashed rgba(255, 255, 255, 0.2);
}



/* 响应式优化 */
@media (max-width: 400px) {
    .binding-item {
        padding: 10px;
    }

    .binding-name {
        font-size: 13px;
    }

    .binding-description {
        font-size: 11px;
    }
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: var(--bg-overlay);
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-overlay-hover);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 按钮样式 - 美化版本 */
.primary-button, .secondary-button {
    width: 100%;
    padding: 14px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.primary-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3), var(--card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.primary-button:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4), var(--card-shadow-lg);
}

.primary-button:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3), var(--card-shadow);
}

.secondary-button {
    background: linear-gradient(135deg, var(--bg-overlay), var(--bg-overlay-hover));
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    box-shadow: var(--card-shadow);
}

.secondary-button:hover {
    background: linear-gradient(135deg, var(--bg-overlay-hover), var(--bg-overlay-active));
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-lg);
    border-color: rgba(255, 255, 255, 0.3);
}

.secondary-button:active {
    transform: translateY(0);
    box-shadow: var(--card-shadow);
}

.primary-button:disabled, .secondary-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 按钮加载状态 */
.primary-button.loading, .secondary-button.loading {
    pointer-events: none;
}

.primary-button.loading::after, .secondary-button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

/* Agent列表样式 */
.agent-list {
    max-height: 250px;
    overflow-y: auto;
    margin-top: 12px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.agent-item {
    padding: 12px 16px;
    margin-bottom: 8px;
    background: var(--bg-overlay);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
}

.agent-item:hover {
    background: var(--bg-overlay-hover);
    transform: translateY(-1px);
    box-shadow: var(--card-shadow-lg);
}

.agent-item.selected {
    background: rgba(37, 99, 235, 0.25);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.agent-name {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
    font-size: 14px;
}

.agent-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 结果区域样式 - 企业级优化 */
.result-area {
    margin-top: 12px;
    padding: 0; /* 移除内边距，由子元素控制 */
    background: transparent; /* 透明背景 */
    border: none; /* 移除边框 */
    /* 移除高度限制和滚动条 */
    color: var(--text-primary);
}

.result-area pre {
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 12px;
    line-height: 1.4;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

.result-area h4 {
    color: rgba(255, 255, 255, 0.95);
    margin: 0 0 8px 0;
    font-size: 13px;
}

/* 企业级模态框样式 - 主题自适应 */
.themed-result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-overlay-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
}

.themed-modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    margin: 0;
    padding: 16px;
}

.themed-modal-content {
    background: var(--modal-bg);
    color: var(--modal-text);
    border-radius: 8px;
    max-width: 90%; /* 限制最大宽度不超过插件宽度 */
    min-width: 320px; /* 减小最小宽度 */
    width: 100%;
    max-height: 85%;
    min-height: 200px; /* 进一步减小最小高度 */
    overflow: hidden;
    box-shadow: var(--modal-shadow);
    border: var(--modal-border);
    display: flex;
    flex-direction: column; /* 使用flex布局 */
}

.themed-modal-header {
    padding: 12px 16px;
    border-bottom: var(--modal-border-internal);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--modal-header-bg);
    color: var(--modal-header-text);
}

.themed-modal-title {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.themed-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--modal-header-text);
    padding: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
    opacity: 0.7;
}

.themed-close-btn:hover {
    background-color: var(--modal-close-hover);
    opacity: 1;
}

.themed-modal-body {
    padding: 12px 16px;
    max-height: 300px; /* 减小最大高度，避免过多空白 */
    overflow-y: auto;
    flex: 1; /* 让body占据剩余空间 */
    display: flex;
    flex-direction: column;
}

.themed-code-block {
    background: var(--modal-code-bg);
    color: var(--modal-code-text);
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 11px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    border: var(--modal-code-border);
    margin: 0;
    max-height: 200px; /* 限制代码块最大高度 */
    overflow-y: auto; /* 内容过多时滚动 */
    line-height: 1.4;
    line-height: 1.4;
}

.themed-modal-footer {
    padding: 12px 16px;
    border-top: var(--modal-border-internal);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--modal-footer-bg);
}

.modal-footer-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.themed-close-button {
    background: var(--modal-button-bg);
    color: var(--modal-button-text);
    border: var(--modal-button-border);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.themed-close-button:hover {
    background: var(--modal-button-hover-bg, var(--modal-button-bg));
    opacity: 0.9;
}

/* 复制按钮样式 */
.themed-copy-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.themed-copy-button:hover {
    background: var(--primary-dark);
    opacity: 0.9;
}

.themed-copy-button:active {
    transform: translateY(1px);
}

.themed-copy-button.copied {
    background: var(--success-color);
}

.themed-copy-button.copied:hover {
    background: var(--success-color);
}

/* 主题适应的表单元素 */
.themed-textarea {
    width: 100%;
    min-height: 200px;
    padding: 12px;
    border: 2px solid var(--modal-border);
    border-radius: 8px;
    background: var(--modal-input-bg);
    color: var(--modal-text);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
    outline: none;
    transition: border-color 0.2s ease;
}

.themed-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.themed-textarea::placeholder {
    color: var(--modal-placeholder);
    opacity: 0.7;
}

/* 主题适应的按钮 */
.themed-primary-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.themed-primary-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.themed-secondary-btn {
    background: var(--modal-secondary-btn);
    color: var(--modal-text);
    border: 2px solid var(--modal-border);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.themed-secondary-btn:hover {
    background: var(--modal-secondary-btn-hover);
    border-color: var(--primary-color);
}

/* 原有模态框样式保持不变 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow: hidden;
    color: #333;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    font-size: 16px;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 16px;
}

.modal-body textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    resize: vertical;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.modal-footer button {
    width: auto;
    margin: 0;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.modal-footer .primary-button {
    background: #007bff;
    color: white;
}

.modal-footer .primary-button:hover {
    background: #0056b3;
}

.modal-footer .secondary-button {
    background: #6c757d;
    color: white;
}

.modal-footer .secondary-button:hover {
    background: #545b62;
}

/* 状态消息 - 美化版本 */
.status-message {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1001;
    animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    letter-spacing: 0.3px;
}

.status-message.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.status-message.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.status-message.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.status-message.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

@keyframes slideUp {
    from {
        transform: translateY(100%) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 重复的样式定义已删除，使用前面的 .result-area 定义 */

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px 6px 0 0;
}

.result-title {
    font-size: 13px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.result-close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 16px;
    line-height: 1;
    transition: all 0.2s ease;
}

.result-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.result-content {
    padding: 12px;
    font-size: 13px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 加载遮罩层样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    text-align: center;
    color: white;
    padding: 30px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 250px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #fff;
}

.loading-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

/* 加载动画变体 */
.loading-spinner.pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 填充结果样式 - 优化版本，无滚动条 */
.fill-result-summary {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.15));
    border: 2px solid var(--success-color);
    border-radius: 12px;
    padding: 18px;
    margin-top: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15), var(--card-shadow);
    position: relative;
    overflow: visible; /* 改为可见，避免滚动条 */
    max-height: none; /* 移除高度限制 */
}

.fill-result-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 2px; /* 内缩2px，避免超出边框 */
    right: 2px; /* 内缩2px，避免超出边框 */
    height: 3px;
    background: linear-gradient(90deg, var(--success-color), #34d399, var(--success-color));
    animation: shimmer 2s ease-in-out infinite;
    border-radius: 1px; /* 添加圆角，与容器风格一致 */
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.result-header h6 {
    margin: 0;
    color: white;
    font-weight: 600;
}

.result-header small {
    color: rgba(255, 255, 255, 0.7);
}

.result-stats {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

.stat-item {
    text-align: center;
    flex: 1;
    padding: 8px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: white;
}

.stat-label {
    display: block;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 2px;
    font-weight: 500;
}

.stat-item.success .stat-number { color: #4ade80; }
.stat-item.failed .stat-number { color: #f87171; }
.stat-item.skipped .stat-number { color: #fbbf24; }
.stat-item.total .stat-number { color: #60a5fa; }

.result-meta {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 11px;
    margin-bottom: 12px;
}

.result-actions {
    text-align: center;
    margin-top: 16px;
}

.result-details-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), var(--card-shadow);
    letter-spacing: 0.3px;
}

.result-details-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #3b82f6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4), var(--card-shadow-lg);
}

.result-details-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), var(--card-shadow);
}

/* 点赞功能样式 */
.rating-section {
    margin-top: 20px;
    padding: 16px;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.rating-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    text-align: center;
}

.rating-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.rating-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rating-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.rating-btn.thumbs-up:hover,
.rating-btn.thumbs-up.active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-color: rgba(16, 185, 129, 0.3);
}

.rating-btn.thumbs-down:hover,
.rating-btn.thumbs-down.active {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
}

.rating-btn:active {
    transform: translateY(0);
}

.rating-btn.active {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浅色主题下的点赞按钮样式 */
[data-theme="light"] .rating-section {
    background: rgba(59, 130, 246, 0.08);
    border-color: rgba(59, 130, 246, 0.15);
}

[data-theme="light"] .rating-title {
    color: var(--text-primary);
}

[data-theme="light"] .rating-btn {
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-secondary);
    border-color: rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .rating-btn:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* 详细结果模态框样式 */
.stat-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 4px;
    color: #374151;
}

.stat-card .stat-label {
    color: #6b7280;
    font-size: 14px;
}

.stat-card.success {
    border-left: 4px solid #10b981;
}
.stat-card.success .stat-number {
    color: #10b981;
}

.stat-card.failed {
    border-left: 4px solid #ef4444;
}
.stat-card.failed .stat-number {
    color: #ef4444;
}

.stat-card.skipped {
    border-left: 4px solid #f59e0b;
}
.stat-card.skipped .stat-number {
    color: #f59e0b;
}

.stat-card.error {
    border-left: 4px solid #8b5cf6;
}
.stat-card.error .stat-number {
    color: #8b5cf6;
}

.control-type-stats {
    max-height: 200px;
    overflow-y: auto;
    background: #f9fafb;
    border-radius: 6px;
    padding: 12px;
}

.control-stat {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 4px 0;
}

.control-type {
    min-width: 120px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.control-numbers {
    min-width: 60px;
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.progress {
    flex: 1;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.result-tabs .tab-content {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 6px 6px;
}

.result-tabs .nav-tabs {
    border-bottom: 1px solid #e5e7eb;
}

.result-tabs .nav-link {
    color: #6b7280;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 8px 16px;
}

.result-tabs .nav-link.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: none;
}

.result-tabs .tab-pane {
    padding: 16px;
}

/* 表格样式优化 */
.table-responsive {
    border-radius: 6px;
    overflow: hidden;
}

.table th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
    font-size: 12px;
    padding: 8px;
}

.table td {
    padding: 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f5f9;
    font-size: 12px;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

.badge {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 12px;
}

.badge.bg-success {
    background-color: #10b981 !important;
}

.badge.bg-danger {
    background-color: #ef4444 !important;
}

/* 简单模态框样式（当Bootstrap不可用时） */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: none;
}

.modal.show {
    display: block !important;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 800px;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: #212529;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    opacity: 0.5;
    cursor: pointer;
}

.btn-close:hover {
    opacity: 0.75;
}

.btn {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    text-decoration: none;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

/* Tab样式 */
.nav-tabs {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    border-bottom: 1px solid #dee2e6;
}

.nav-item {
    margin-bottom: -1px;
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    cursor: pointer;
}

.nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    margin-top: 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 企业级紧凑详情面板样式 */
.compact-details-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-overlay-bg);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    backdrop-filter: blur(3px);
}

.compact-details-container {
    background: var(--modal-bg);
    border-radius: 8px;
    width: 100%;
    max-width: 420px;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    box-shadow: var(--modal-shadow);
    border: var(--modal-border);
    overflow: hidden;
}

.compact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--modal-header-bg);
    color: var(--modal-header-text);
    border-bottom: var(--modal-border-internal);
}

.compact-header h6 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.compact-close {
    background: none;
    border: none;
    color: var(--modal-header-text);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
    opacity: 0.7;
}

.compact-close:hover {
    background-color: var(--modal-close-hover);
    opacity: 1;
}

.compact-body {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    color: var(--modal-text);
}

/* 快速统计 */
.quick-stats {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.quick-stat {
    flex: 1;
    text-align: center;
    padding: 12px 8px;
    border-radius: 8px;
    background: #f8fafc;
    border: 2px solid transparent;
}

.quick-stat.success {
    border-color: #10b981;
}

.quick-stat.failed {
    border-color: #ef4444;
}

.quick-stat.skipped {
    border-color: #f59e0b;
}

.quick-stat .number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #374151;
}

.quick-stat.success .number {
    color: #10b981;
}

.quick-stat.failed .number {
    color: #ef4444;
}

.quick-stat.skipped .number {
    color: #f59e0b;
}

.quick-stat .label {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-top: 2px;
}

/* 紧凑Tab */
.compact-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.compact-tab {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    color: #6b7280;
}

.compact-tab:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

.compact-tab.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 结果列表 */
.compact-results {
    position: relative;
}

.compact-tab-content {
    display: none;
}

.compact-tab-content.active {
    display: block;
}

.compact-list {
    max-height: 300px;
    overflow-y: auto;
}

.compact-empty {
    text-align: center;
    color: #6b7280;
    padding: 40px 20px;
    font-size: 14px;
}

.compact-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    background: white;
    transition: box-shadow 0.2s;
}

.compact-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.compact-item.success {
    border-left: 4px solid #10b981;
}

.compact-item.failed {
    border-left: 4px solid #ef4444;
}

.compact-item-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.status-icon {
    font-size: 14px;
}

.field-name {
    font-weight: 500;
    color: #374151;
    font-size: 13px;
    flex: 1;
}

.compact-item-body {
    font-size: 12px;
}

.item-value {
    color: #1f2937;
    margin-bottom: 4px;
    font-family: monospace;
    background: #f8fafc;
    padding: 4px 6px;
    border-radius: 4px;
    word-break: break-all;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.control-type {
    color: #6b7280;
    font-size: 11px;
    background: #e5e7eb;
    padding: 2px 6px;
    border-radius: 4px;
}

.error-reason {
    color: #ef4444;
    font-size: 11px;
    font-style: italic;
}

/* 表单数据模态框样式 */
.form-data-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.form-data-container {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.form-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
}

.form-data-header h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.form-data-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.form-data-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.form-data-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 统计信息 */
.form-data-stats {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 12px 8px;
    border-radius: 8px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
}

.stat-item .stat-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #1e293b;
}

.stat-item .stat-label {
    display: block;
    font-size: 12px;
    color: #64748b;
    margin-top: 2px;
}

/* Tab样式 */
.form-data-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.form-data-tab {
    padding: 8px 12px;
    border: none;
    background: none;
    border-radius: 6px 6px 0 0;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    color: #64748b;
    border-bottom: 2px solid transparent;
}

.form-data-tab:hover {
    color: #4f46e5;
    background: #f1f5f9;
}

.form-data-tab.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
    background: #f1f5f9;
}

/* 内容区域 */
.form-data-content {
    position: relative;
    min-height: 200px;
}

.form-data-tab-content {
    display: none;
}

.form-data-tab-content.active {
    display: block;
}

.form-data-list {
    max-height: 300px;
    overflow-y: auto;
}

.form-data-empty {
    text-align: center;
    color: #64748b;
    padding: 40px 20px;
    font-size: 14px;
}

.form-data-item {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    background: white;
    transition: box-shadow 0.2s;
}

.form-data-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-data-item.filled {
    border-left: 4px solid #10b981;
}

.form-data-item.empty {
    border-left: 4px solid #f59e0b;
}

.form-data-item-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.form-data-item-header .status-icon {
    font-size: 14px;
}

.form-data-item-header .field-name {
    font-weight: 500;
    color: #1e293b;
    font-size: 13px;
    flex: 1;
}

.form-data-item-header .field-type {
    background: #e2e8f0;
    color: #64748b;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
}

.form-data-item-body {
    font-size: 12px;
}

.field-value {
    color: #1e293b;
    margin-bottom: 4px;
    font-family: monospace;
    background: #f8fafc;
    padding: 4px 6px;
    border-radius: 4px;
    word-break: break-all;
}

.field-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.field-meta span {
    color: #64748b;
    font-size: 10px;
}

/* 操作按钮 */
.form-data-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
}

.form-data-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #64748b;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.form-data-btn:hover {
    border-color: #4f46e5;
    color: #4f46e5;
    background: #f1f5f9;
}

.form-data-btn.secondary {
    background: #f8fafc;
}

/* 主题切换器样式 */
.theme-switcher {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-overlay);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 16px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.theme-switcher-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.theme-switcher-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.theme-switcher-toggle {
    background: var(--bg-overlay);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 16px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
}

.theme-switcher-toggle:hover {
    background: var(--bg-overlay-hover);
    transform: translateY(-1px);
    box-shadow: var(--card-shadow-lg);
}

.theme-options {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 4px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.theme-options::-webkit-scrollbar {
    display: none;
}

.theme-option {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-option.active {
    border-color: var(--text-primary);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.theme-option.active::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 主题预览色彩 */
.theme-option[data-theme="default"] {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.theme-option[data-theme="light"] {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.theme-option[data-theme="tech-blue"] {
    background: linear-gradient(135deg, #0c4a6e 0%, #075985 100%);
}

/* 主题切换器收起状态 */
.theme-switcher.collapsed .theme-options {
    display: none;
    opacity: 0;
    transform: translateY(-10px);
}

.theme-switcher.collapsed {
    padding: 8px 16px;
}

.theme-switcher:not(.collapsed) .theme-options {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 主题切换器切换按钮状态指示 */
.theme-switcher.collapsed .theme-switcher-toggle {
    opacity: 0.7;
}

.theme-switcher:not(.collapsed) .theme-switcher-toggle {
    opacity: 1;
    background: var(--bg-overlay-hover);
}

/* 响应式优化 */
@media (max-width: 400px) {
    .theme-switcher {
        padding: 10px 12px;
    }

    .theme-option {
        width: 36px;
        height: 36px;
    }

    .theme-options {
        gap: 6px;
    }
}
