# 表单回填记录 API 使用指南

## 概述

表单回填记录系统用于记录用户通过Chrome插件进行表单自动填写的操作记录，包括回填结果、用户反馈（点赞/踩）等信息。

## 数据库表结构

### form_fill_records 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 回填记录ID（主键） |
| recognition_record_id | BIGINT | 关联的识别记录ID |
| page_binding_id | BIGINT | 页面绑定配置ID |
| target_url | VARCHAR(1000) | 目标页面URL |
| fill_data | JSON | 回填的数据内容 |
| fill_result | TINYINT | 回填结果：1-成功，2-部分成功，3-失败 |
| error_message | TEXT | 错误信息 |
| user_id | BIGINT | 执行回填的用户ID |
| user_feedback | TINYINT | 用户反馈：1-点赞，0-踩，NULL-无反馈 |
| feedback_time | DATETIME | 反馈时间 |
| feedback_comment | VARCHAR(500) | 反馈备注 |
| fill_time | DATETIME | 回填时间 |
| created_time | DATETIME | 创建时间 |
| updated_time | DATETIME | 更新时间 |
| deleted | TINYINT | 删除标记 |

## API 接口

### 1. 创建回填记录

**接口地址：** `POST /api/v1/form-fill-records`

**请求参数：**
```json
{
  "recognitionRecordId": 123,
  "pageBindingId": 456,
  "targetUrl": "https://example.com/form",
  "fillData": "{\"name\":\"张三\",\"age\":30,\"email\":\"<EMAIL>\"}",
  "fillResult": 1,
  "errorMessage": null,
  "feedbackComment": null
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 789,
    "recognitionRecordId": 123,
    "pageBindingId": 456,
    "bindingName": "用户注册表单",
    "targetUrl": "https://example.com/form",
    "fillData": "{\"name\":\"张三\",\"age\":30,\"email\":\"<EMAIL>\"}",
    "fillResult": 1,
    "fillResultDesc": "成功",
    "userId": 1,
    "userName": "testuser",
    "userRealName": "测试用户",
    "userFeedback": null,
    "userFeedbackDesc": "无反馈",
    "fillTime": "2024-01-20 10:30:00",
    "createdTime": "2024-01-20 10:30:00"
  }
}
```

### 2. 更新用户反馈（点赞/踩）

**接口地址：** `PUT /api/v1/form-fill-records/feedback`

**请求参数：**
```json
{
  "recordId": 789,
  "userFeedback": 1,
  "feedbackComment": "回填效果很好，节省了很多时间！"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 789,
    "userFeedback": 1,
    "userFeedbackDesc": "点赞",
    "feedbackTime": "2024-01-20 10:35:00",
    "feedbackComment": "回填效果很好，节省了很多时间！"
  }
}
```

### 3. 分页查询回填记录

**接口地址：** `GET /api/v1/form-fill-records/page`

**请求参数：**
- page: 页码（默认1）
- size: 每页大小（默认10）
- keyword: 搜索关键词（可选）
- fillResult: 回填结果筛选（可选）
- userFeedback: 用户反馈筛选（可选）
- userId: 用户ID筛选（可选）
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 4. 获取回填统计信息

**接口地址：** `GET /api/v1/form-fill-records/statistics`

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalRecords": 1000,
    "successRecords": 850,
    "successRate": 85.0,
    "fillResultStats": [
      {"fillResult": 1, "fillResultName": "成功", "count": 850},
      {"fillResult": 2, "fillResultName": "部分成功", "count": 100},
      {"fillResult": 3, "fillResultName": "失败", "count": 50}
    ],
    "userFeedbackStats": [
      {"feedbackType": "LIKE", "feedbackName": "点赞", "count": 300},
      {"feedbackType": "DISLIKE", "feedbackName": "踩", "count": 50},
      {"feedbackType": "NO_FEEDBACK", "feedbackName": "无反馈", "count": 650}
    ]
  }
}
```

## Chrome插件集成示例

### 1. 创建回填记录

```javascript
// 在Chrome插件中，表单回填完成后调用
async function createFillRecord(recognitionRecordId, pageBindingId, fillData, fillResult, errorMessage = null) {
    const requestData = {
        recognitionRecordId: recognitionRecordId,
        pageBindingId: pageBindingId,
        targetUrl: window.location.href,
        fillData: JSON.stringify(fillData),
        fillResult: fillResult, // 1-成功, 2-部分成功, 3-失败
        errorMessage: errorMessage
    };

    try {
        const response = await fetch('/api/v1/form-fill-records', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('回填记录创建成功:', result.data);
            return result.data;
        } else {
            console.error('创建回填记录失败:', result.message);
            return null;
        }
    } catch (error) {
        console.error('创建回填记录异常:', error);
        return null;
    }
}
```

### 2. 用户反馈处理

```javascript
// 处理用户点赞/踩操作
async function handleUserFeedback(recordId, isLike, comment = null) {
    const requestData = {
        recordId: recordId,
        userFeedback: isLike ? 1 : 0, // 1-点赞, 0-踩
        feedbackComment: comment
    };

    try {
        const response = await fetch('/api/v1/form-fill-records/feedback', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getAuthToken()}`
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();
        if (result.code === 200) {
            console.log('用户反馈更新成功:', result.data);
            // 更新UI显示
            updateFeedbackUI(recordId, isLike);
            return true;
        } else {
            console.error('更新用户反馈失败:', result.message);
            return false;
        }
    } catch (error) {
        console.error('更新用户反馈异常:', error);
        return false;
    }
}

// 更新反馈UI
function updateFeedbackUI(recordId, isLike) {
    const likeBtn = document.querySelector(`[data-record-id="${recordId}"] .like-btn`);
    const dislikeBtn = document.querySelector(`[data-record-id="${recordId}"] .dislike-btn`);
    
    if (isLike) {
        likeBtn.classList.add('active');
        dislikeBtn.classList.remove('active');
    } else {
        likeBtn.classList.remove('active');
        dislikeBtn.classList.add('active');
    }
}
```

### 3. 完整的表单回填流程

```javascript
// 完整的表单回填流程
async function performFormFill(recognitionRecordId, pageBindingId, recognitionData) {
    let fillResult = 1; // 默认成功
    let errorMessage = null;
    let fillData = {};

    try {
        // 1. 解析识别数据
        const parsedData = JSON.parse(recognitionData);
        
        // 2. 执行表单回填
        for (const [fieldName, fieldValue] of Object.entries(parsedData)) {
            try {
                const success = await fillFormField(fieldName, fieldValue);
                fillData[fieldName] = fieldValue;
                
                if (!success) {
                    fillResult = 2; // 部分成功
                }
            } catch (error) {
                console.error(`填写字段 ${fieldName} 失败:`, error);
                fillResult = 2; // 部分成功
            }
        }

        // 3. 创建回填记录
        const fillRecord = await createFillRecord(
            recognitionRecordId, 
            pageBindingId, 
            fillData, 
            fillResult, 
            errorMessage
        );

        // 4. 显示反馈UI
        if (fillRecord) {
            showFeedbackUI(fillRecord.id);
        }

        return fillRecord;

    } catch (error) {
        console.error('表单回填失败:', error);
        
        // 创建失败记录
        await createFillRecord(
            recognitionRecordId, 
            pageBindingId, 
            fillData, 
            3, // 失败
            error.message
        );
        
        return null;
    }
}

// 显示反馈UI
function showFeedbackUI(recordId) {
    const feedbackHtml = `
        <div class="fill-feedback" data-record-id="${recordId}">
            <span>回填效果如何？</span>
            <button class="like-btn" onclick="handleUserFeedback(${recordId}, true)">
                👍 好用
            </button>
            <button class="dislike-btn" onclick="handleUserFeedback(${recordId}, false)">
                👎 不好用
            </button>
        </div>
    `;
    
    // 将反馈UI添加到页面合适位置
    document.body.insertAdjacentHTML('beforeend', feedbackHtml);
}
```

## 使用场景

1. **表单回填完成后**：自动创建回填记录，记录回填结果和数据
2. **用户反馈收集**：用户可以对回填效果进行点赞或踩的反馈
3. **数据统计分析**：管理员可以查看回填成功率、用户满意度等统计信息
4. **问题排查**：通过错误信息和回填数据，快速定位和解决问题
5. **功能优化**：基于用户反馈和统计数据，持续优化回填算法和页面绑定配置

## 注意事项

1. 所有API都需要用户登录认证
2. 用户只能操作自己的回填记录
3. 回填数据以JSON格式存储，便于后续分析
4. 支持软删除，删除的记录不会物理删除
5. 所有操作都会记录操作日志，便于审计和追踪
