<template>
  <div class="history-page">
    <Header />

    <main class="main-content" style="    padding-top: 0.75rem;">
      <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2><i class="fas fa-history"></i> 调用历史</h2>
                <p class="text-muted">查看您的智能体矩阵调用记录和详细信息</p>
              </div>
              <div>
                <div class="btn-group" role="group" aria-label="视图切换">
                  <button
                    type="button"
                    class="btn btn-outline-primary view-toggle-btn"
                    :class="{ active: currentView === 'table' }"
                    @click="switchView('table')"
                  >
                    <i class="fas fa-table me-1"></i> 表格
                  </button>
                  <button
                    type="button"
                    class="btn btn-outline-primary view-toggle-btn"
                    :class="{ active: currentView === 'card' }"
                    @click="switchView('card')"
                  >
                    <i class="fas fa-th-large me-1"></i> 卡片
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-primary mb-2">
                  {{ animatedStats.total }}
                </h2>
                <p class="stat-label text-muted mb-0">总调用次数</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-success mb-2">
                  {{ animatedStats.success }}
                </h2>
                <p class="stat-label text-muted mb-0">成功调用</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-danger mb-2">
                  {{ animatedStats.failed }}
                </h2>
                <p class="stat-label text-muted mb-0">失败调用</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card stat-card-modern shadow-sm border-0">
              <div class="card-body text-center p-4">
                <h2 class="stat-number text-info mb-2">
                  {{ animatedStats.successRate }}%
                </h2>
                <p class="stat-label text-muted mb-0">成功率</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <form @submit.prevent="handleSearch" class="search-form">
                  <div class="row g-3">
                    <div class="col-md-4">
                      <label for="agentName" class="form-label">Agent名称</label>
                      <input
                        type="text"
                        class="form-control"
                        id="agentName"
                        v-model="searchForm.agentName"
                        placeholder="搜索Agent名称"
                      >
                    </div>
                    <div class="col-md-3">
                      <label for="callType" class="form-label">调用类型</label>
                      <select class="form-select" id="callType" v-model="searchForm.callType">
                        <option value="">全部类型</option>
                        <option value="API">API调用</option>
                        <option value="PLUGIN">插件调用</option>
                      </select>
                    </div>
                    <div class="col-md-3">
                      <label for="dateRange" class="form-label">时间范围</label>
                      <select class="form-select" id="dateRange" v-model="searchForm.dateRange">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">最近一周</option>
                        <option value="month">最近一月</option>
                      </select>
                    </div>
                    <div class="col-md-2">
                      <label class="form-label">&nbsp;</label>
                      <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                          <i class="fas fa-search"></i> 搜索
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- 调用历史内容区域 -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-list"></i> 调用记录
                  <span class="badge bg-secondary ms-2">{{ historyRecords.length }}</span>
                </h5>
              </div>
              <div class="card-body">
                <!-- 表格视图 -->
                <div v-show="currentView === 'table'" id="tableViewContainer">
                  <div v-if="historyRecords.length > 0" class="table-responsive">
                    <table class="table table-hover align-middle">
                      <thead class="table-dark">
                        <tr>
                          <th width="5%">#</th>
                          <th width="15%">Agent & 任务</th>
                          <th width="10%">类型</th>
                          <th width="10%">状态</th>
                          <th width="12%">性能</th>
                          <th width="15%">调用时间</th>
                          <th width="15%">来源信息</th>
                          <th width="18%">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="history in historyRecords"
                          :key="history.id"
                          class="history-row"
                        >
                          <!-- ID列 -->
                          <td>
                            <small class="text-muted">#{{ history.id }}</small>
                          </td>

                          <!-- Agent名称和任务类型 -->
                          <td>
                            <div>
                              <strong>{{ history.agentName }}</strong>
                            </div>
                            <small v-if="history.taskType" class="text-muted">
                              <i class="fas fa-tag"></i>
                              {{ history.taskType }}
                            </small>
                          </td>

                          <!-- 调用类型 -->
                          <td>
                            <span v-if="history.callType === 'API'" class="badge bg-primary">
                              <i class="fas fa-code"></i> API
                            </span>
                            <span v-else-if="history.callType === 'PLUGIN'" class="badge bg-info">
                              <i class="fas fa-plug"></i> 插件
                            </span>
                          </td>

                          <!-- 调用状态 -->
                          <td>
                            <div>
                              <span :class="`badge ${getCallStatusClass(history.callStatus)}`">
                                <i :class="history.callStatus === 'SUCCESS' ? 'fas fa-check' :
                                          history.callStatus === 'FAILED' ? 'fas fa-times' :
                                          history.callStatus === 'TIMEOUT' ? 'fas fa-clock' : 'fas fa-question'"></i>
                                {{ getCallStatusText(history.callStatus) }}
                              </span>
                              <div v-if="history.processStatus" class="mt-1">
                                <small :class="`badge ${getProcessStatusClass(history.processStatus)}`">
                                  <i :class="history.processStatus === 'COMPLETED' ? 'fas fa-check-circle' :
                                            history.processStatus === 'PROCESSING' ? 'fas fa-spinner fa-spin' :
                                            history.processStatus === 'PENDING' ? 'fas fa-clock' : 'fas fa-times-circle'"></i>
                                  {{ getProcessStatusText(history.processStatus) }}
                                </small>
                              </div>
                            </div>
                            <div v-if="history.errorMessage" class="mt-1">
                              <small
                                class="text-danger"
                                :title="history.errorMessage"
                              >
                                {{ truncateText(history.errorMessage, 20) }}
                              </small>
                            </div>
                          </td>

                          <!-- 性能信息 -->
                          <td>
                            <div v-if="history.responseTime != null">
                              <span
                                :class="getPerformanceBadgeClass(history.responseTime)"
                              >
                                {{ formatResponseTime(history.responseTime) }}
                              </span>
                              <div class="mt-1">
                                <small class="text-muted">
                                  {{ getPerformanceText(history.responseTime) }}
                                </small>
                              </div>
                            </div>
                            <span v-else class="text-muted">-</span>
                          </td>

                          <!-- 调用时间 -->
                          <td>
                            <div>
                              {{ formatDate(history.createTime, 'MM-DD HH:mm') }}
                            </div>
                            <small class="text-muted">{{ formatDate(history.createTime, 'YYYY') }}</small>
                          </td>

                          <!-- 来源信息 -->
                          <td>
                            <div v-if="history.callIp">
                              <small class="text-muted">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ history.callIp }}
                              </small>
                            </div>
                            <div v-if="history.uploadContentType">
                              <small class="badge bg-light text-dark mt-1">
                                <i class="fas fa-file"></i>
                                {{ history.uploadContentType }}
                              </small>
                            </div>
                          </td>

                          <!-- 操作按钮 -->
                          <td>
                            <div class="action-buttons d-flex gap-1">
                              <button
                                class="btn btn-sm btn-outline-primary view-details"
                                @click="viewDetails(history)"
                                title="查看详情"
                              >
                                <i class="fas fa-eye"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- 表格视图空状态 -->
                  <div v-else class="text-center py-5">
                    <i class="fas fa-history fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无调用记录</h4>
                    <p class="text-muted">您还没有任何 SINAIR-AGENT 调用记录</p>
                  </div>
                </div>

                <!-- 卡片视图 -->
                <div v-show="currentView === 'card'" id="cardViewContainer">
                  <div v-if="historyRecords.length > 0" class="row">
                    <div
                      v-for="history in historyRecords"
                      :key="history.id"
                      class="col-md-6 col-lg-4 mb-3"
                    >
                      <div
                        class="card history-card h-100"
                        @click="viewDetails(history)"
                        style="cursor: pointer;"
                      >
                        <div class="card-header d-flex justify-content-between align-items-center">
                          <small class="text-muted">#{{ history.id }}</small>
                          <span
                            v-if="history.callStatus === 'SUCCESS'"
                            class="badge bg-success status-badge"
                          >
                            <i class="fas fa-check"></i> 成功
                          </span>
                          <span
                            v-else-if="history.callStatus === 'FAILED'"
                            class="badge bg-danger status-badge"
                          >
                            <i class="fas fa-times"></i> 失败
                          </span>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">
                            <i class="fas fa-robot text-primary"></i>
                            {{ history.agentName }}
                          </h6>
                          <div class="mb-2">
                            <span v-if="history.callType === 'API'" class="badge bg-primary me-1">
                              <i class="fas fa-code"></i> API
                            </span>
                            <span v-else-if="history.callType === 'PLUGIN'" class="badge bg-info me-1">
                              <i class="fas fa-plug"></i> 插件
                            </span>
                            <span v-if="history.taskType" class="badge bg-secondary">
                              {{ history.taskType }}
                            </span>
                          </div>
                          <div class="small text-muted mb-2">
                            <div>
                              <i class="fas fa-clock"></i>
                              {{ formatDate(history.createTime, 'YYYY-MM-DD HH:mm') }}
                            </div>
                            <div v-if="history.responseTime != null">
                              <span
                                class="performance-indicator"
                                :class="getPerformanceBgClass(history.responseTime)"
                              ></span>
                              <i class="fas fa-tachometer-alt"></i>
                              {{ history.responseTime }}ms
                            </div>
                            <div v-if="history.callIp">
                              <i class="fas fa-map-marker-alt"></i>
                              {{ history.callIp }}
                            </div>
                          </div>
                          <div v-if="history.errorMessage" class="alert alert-danger alert-sm p-2 mb-0">
                            <small>
                              <i class="fas fa-exclamation-triangle"></i>
                              {{ truncateText(history.errorMessage, 50) }}
                            </small>
                          </div>
                        </div>
                        <div class="card-footer">
                          <small class="text-muted">
                            <i class="fas fa-eye"></i> 点击查看详情
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 卡片视图空状态 -->
                  <div v-else class="text-center py-5">
                    <i class="fas fa-history fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无调用记录</h4>
                    <p class="text-muted">您还没有任何 SINAIR-AGENT 调用记录</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <div class="row mt-4" v-if="historyRecords.length > 0">
          <div class="col-12">
            <div class="pagination-container">
              <div class="d-flex justify-content-between align-items-center">
                <!-- 左侧：分页信息和每页显示条数 -->
                <div class="d-flex align-items-center gap-3">
                  <!-- 分页信息 -->
                  <div class="pagination-info">
                    <span class="text-muted">
                      显示第 <strong>{{ startRecord }}</strong>
                      到 <strong>{{ endRecord }}</strong>
                      条，共 <strong>{{ totalRecords }}</strong> 条记录
                    </span>
                  </div>

                  <!-- 每页显示条数 -->
                  <div class="page-size-selector d-flex align-items-center gap-2">
                    <span class="text-muted small">每页</span>
                    <select
                      class="form-select form-select-sm"
                      v-model="pageSize"
                      @change="changePageSize"
                      style="width: auto; min-width: 80px;"
                    >
                      <option value="5">5</option>
                      <option value="10">10</option>
                      <option value="20">20</option>
                      <option value="50">50</option>
                    </select>
                    <span class="text-muted small">条</span>
                  </div>
                </div>

                <!-- 右侧：分页控件 -->
                <nav aria-label="调用历史分页">
                  <ul class="pagination mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                        上一页
                      </button>
                    </li>
                    <li
                      v-for="page in visiblePages"
                      :key="page"
                      class="page-item"
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="changePage(page)">{{ page }}</button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                        下一页
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 详情弹框 -->
    <div
      class="modal fade"
      id="detailModal"
      tabindex="-1"
      aria-labelledby="detailModalLabel"
      aria-hidden="true"
      ref="detailModal"
    >
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="detailModalLabel">
              <i class="fas fa-info-circle text-primary me-2"></i>调用详情
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" v-if="selectedHistory">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label fw-bold">Agent名称</label>
                <div class="form-control-plaintext">{{ selectedHistory.agentName }}</div>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-bold">调用类型</label>
                <div class="form-control-plaintext">
                  <span v-if="selectedHistory.callType === 'API'" class="badge bg-primary">
                    <i class="fas fa-code"></i> API调用
                  </span>
                  <span v-else-if="selectedHistory.callType === 'PLUGIN'" class="badge bg-info">
                    <i class="fas fa-plug"></i> 插件调用
                  </span>
                </div>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-bold">调用状态</label>
                <div class="form-control-plaintext">
                  <span v-if="selectedHistory.callStatus === 'SUCCESS'" class="badge bg-success">
                    <i class="fas fa-check"></i> 成功
                  </span>
                  <span v-else-if="selectedHistory.callStatus === 'FAILED'" class="badge bg-danger">
                    <i class="fas fa-times"></i> 失败
                  </span>
                </div>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-bold">响应时间</label>
                <div class="form-control-plaintext">
                  <span v-if="selectedHistory.responseTime != null">
                    {{ selectedHistory.responseTime }}ms
                    <small class="text-muted ms-2">
                      ({{ getPerformanceText(selectedHistory.responseTime) }})
                    </small>
                  </span>
                  <span v-else class="text-muted">-</span>
                </div>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-bold">调用时间</label>
                <div class="form-control-plaintext">{{ formatDate(selectedHistory.createTime, 'YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
              <div class="col-md-6">
                <label class="form-label fw-bold">来源IP</label>
                <div class="form-control-plaintext">{{ selectedHistory.callIp || '-' }}</div>
              </div>
              <div class="col-12" v-if="selectedHistory.errorMessage">
                <label class="form-label fw-bold text-danger">错误信息</label>
                <div class="alert alert-danger">
                  <pre class="mb-0">{{ selectedHistory.errorMessage }}</pre>
                </div>
              </div>

              <!-- 文件信息和预览 -->
              <div class="col-12" v-if="hasFileInfo">
                <hr class="my-4">
                <h6 class="fw-bold mb-3">
                  <i class="fas fa-file-alt text-info me-2"></i>文件信息
                </h6>

                <!-- 文件基本信息 -->
                <div class="row g-3 mb-3">
                  <div class="col-md-4" v-if="selectedHistory.uploadContentType">
                    <label class="form-label fw-bold">内容类型</label>
                    <div class="form-control-plaintext">
                      <span :class="getContentTypeBadgeClass(selectedHistory.uploadContentType)">
                        <i :class="getContentTypeIcon(selectedHistory.uploadContentType)"></i>
                        {{ getContentTypeText(selectedHistory.uploadContentType) }}
                      </span>
                    </div>
                  </div>
                  <div class="col-md-4" v-if="selectedHistory.taskType">
                    <label class="form-label fw-bold">任务类型</label>
                    <div class="form-control-plaintext">
                      <span class="badge bg-secondary">{{ getTaskTypeText(selectedHistory.taskType) }}</span>
                    </div>
                  </div>
                  <div class="col-md-4" v-if="selectedHistory.confidenceScore">
                    <label class="form-label fw-bold">置信度</label>
                    <div class="form-control-plaintext">
                      <span :class="getConfidenceBadgeClass(selectedHistory.confidenceScore)">
                        {{ (selectedHistory.confidenceScore * 100).toFixed(1) }}%
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 文件列表和预览 -->
                <div v-if="fileList.length > 0" class="mb-3">
                  <label class="form-label fw-bold">上传文件</label>
                  <div class="file-list">
                    <div
                      v-for="(file, index) in fileList"
                      :key="index"
                      class="file-item card mb-2"
                    >
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center justify-content-between">
                          <div class="file-info">
                            <h6 class="mb-1">
                              <i :class="getFileIcon(file.type)" class="me-2"></i>
                              {{ file.name || `文件${index + 1}` }}
                            </h6>
                            <small class="text-muted">
                              {{ file.type || 'unknown' }}
                              <span v-if="file.size">• {{ formatFileSize(file.size) }}</span>
                            </small>
                          </div>
                          <div class="file-actions">
                            <button
                              v-if="canPreview(file.type)"
                              @click="previewFileMethod(file, index)"
                              class="btn btn-sm btn-outline-primary"
                              type="button"
                            >
                              <i class="fas fa-eye"></i> 预览
                            </button>
                            <span v-else class="text-muted small">
                              <i class="fas fa-file"></i> 不支持预览
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 原始文本内容 -->
                <div v-if="selectedHistory.originalText" class="mb-3">
                  <label class="form-label fw-bold">原始文本内容</label>
                  <div class="card">
                    <div class="card-body">
                      <pre class="mb-0 text-wrap">{{ selectedHistory.originalText }}</pre>
                    </div>
                  </div>
                </div>

                <!-- 解析结果 -->
                <div v-if="selectedHistory.parseResult" class="mb-3">
                  <label class="form-label fw-bold">解析结果</label>
                  <div class="card">
                    <div class="card-body">
                      <pre class="mb-0 text-wrap">{{ formatJsonString(selectedHistory.parseResult) }}</pre>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 响应结果 -->
              <div class="col-12" v-if="selectedHistory.responseData">
                <hr class="my-4">
                <h6 class="fw-bold mb-3">
                  <i class="fas fa-reply text-success me-2"></i>响应结果
                </h6>

                <div class="mb-3">
                  <label class="form-label fw-bold">响应数据</label>
                  <textarea
                    class="form-control response-textarea"
                    :value="formatJsonString(selectedHistory.responseData)"
                    readonly
                    rows="12"
                    style="font-family: 'Courier New', monospace; font-size: 0.875rem; line-height: 1.4;"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i> 关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览模态框 -->
    <div
      class="modal fade"
      id="filePreviewModal"
      tabindex="-1"
      aria-labelledby="filePreviewModalLabel"
      aria-hidden="true"
      ref="filePreviewModal"
    >
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="filePreviewModalLabel">
              <i class="fas fa-eye text-primary me-2"></i>文件预览
              <span v-if="previewFile.name" class="text-muted ms-2">- {{ previewFile.name }}</span>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-0" v-if="previewFile.url">
            <div class="preview-container">
              <!-- PDF预览 -->
              <div v-if="previewFile.type === 'pdf'" class="pdf-preview text-center p-5">
                <div class="pdf-preview-placeholder">
                  <i class="fas fa-file-pdf fa-5x text-danger mb-4"></i>
                  <h4 class="mb-3">PDF文档预览</h4>
                  <p class="text-muted mb-4">
                    由于浏览器安全限制，PDF文件无法在弹框中直接预览。<br>
                    请点击下方按钮在新窗口中打开PDF文件。
                  </p>
                  <div class="d-flex justify-content-center gap-3">
                    <button
                      type="button"
                      class="btn btn-primary"
                      @click="openPdfInNewWindow"
                    >
                      <i class="fas fa-external-link-alt me-2"></i>
                      在新窗口中打开
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="copyPdfUrl"
                    >
                      <i class="fas fa-copy me-2"></i>
                      复制链接
                    </button>
                  </div>
                </div>
              </div>

              <!-- 图片预览 -->
              <div v-else-if="isImageType(previewFile.type)" class="image-preview text-center p-3">
                <img
                  :src="previewFile.url"
                  :alt="previewFile.name"
                  class="img-fluid"
                  style="max-height: 70vh; max-width: 100%;"
                  @error="handleImageError"
                />
              </div>

              <!-- 文本预览 -->
              <div v-else-if="isTextType(previewFile.type)" class="text-preview p-3">
                <pre class="text-wrap">{{ previewFile.content || '正在加载...' }}</pre>
              </div>

              <!-- 不支持预览的文件类型 -->
              <div v-else class="unsupported-preview text-center p-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">此文件类型不支持预览</h5>
                <p class="text-muted">该文件类型暂不支持在线预览</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i> 关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import dayjs from 'dayjs'
import api from '@/utils/api'
import { getApiBaseUrl } from '@/utils/env'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const currentView = ref('table')
const historyRecords = ref([])
const selectedHistory = ref(null)
const detailModal = ref(null)
const filePreviewModal = ref(null)
const loading = ref(false)

// 文件预览相关
const previewFile = ref({
  name: '',
  type: '',
  url: '',
  content: ''
})
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  agentName: '',
  callType: '',
  callStatus: ''
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(0)

// 统计数据
const stats = reactive({
  total: 0,
  success: 0,
  failed: 0,
  successRate: 0
})

const animatedStats = reactive({
  total: 0,
  success: 0,
  failed: 0,
  successRate: 0
})

// 计算属性
const hasFileInfo = computed(() => {
  if (!selectedHistory.value) return false
  return selectedHistory.value.uploadFileInfo ||
         selectedHistory.value.uploadFileUrls ||
         selectedHistory.value.originalText ||
         selectedHistory.value.parseResult ||
         selectedHistory.value.uploadContentType
})

const totalPages = computed(() => {
  return Math.ceil(totalRecords.value / pageSize.value)
})

const startRecord = computed(() => {
  return (currentPage.value - 1) * pageSize.value + 1
})

const endRecord = computed(() => {
  return Math.min(currentPage.value * pageSize.value, totalRecords.value)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 方法
const switchView = (view) => {
  currentView.value = view
  localStorage.setItem('historyView', view)
}

const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm') => {
  if (!dateString) return '未知'
  return dayjs(dateString).format(format)
}

// 格式化响应时间
const formatResponseTime = (responseTime) => {
  if (!responseTime) return '未知'
  if (responseTime < 1000) {
    return `${responseTime}ms`
  } else {
    return `${(responseTime / 1000).toFixed(2)}s`
  }
}

// 获取调用状态文本
const getCallStatusText = (status) => {
  switch (status) {
    case 'SUCCESS':
      return '成功'
    case 'FAILED':
      return '失败'
    case 'TIMEOUT':
      return '超时'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

// 获取调用状态CSS类
const getCallStatusClass = (status) => {
  switch (status) {
    case 'SUCCESS':
      return 'bg-success'
    case 'FAILED':
      return 'bg-danger'
    case 'TIMEOUT':
      return 'bg-warning'
    case 'CANCELLED':
      return 'bg-secondary'
    default:
      return 'bg-secondary'
  }
}

// 获取处理状态文本
const getProcessStatusText = (status) => {
  switch (status) {
    case 'PENDING':
      return '等待中'
    case 'PROCESSING':
      return '处理中'
    case 'COMPLETED':
      return '已完成'
    case 'FAILED':
      return '处理失败'
    default:
      return '未知'
  }
}

// 获取处理状态CSS类
const getProcessStatusClass = (status) => {
  switch (status) {
    case 'PENDING':
      return 'bg-info'
    case 'PROCESSING':
      return 'bg-warning'
    case 'COMPLETED':
      return 'bg-success'
    case 'FAILED':
      return 'bg-danger'
    default:
      return 'bg-secondary'
  }
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

const getPerformanceBadgeClass = (responseTime) => {
  if (responseTime > 3000) return 'badge bg-danger'
  if (responseTime > 1000) return 'badge bg-warning'
  if (responseTime > 500) return 'badge bg-info'
  return 'badge bg-success'
}

const getPerformanceBgClass = (responseTime) => {
  if (responseTime > 3000) return 'bg-danger'
  if (responseTime > 1000) return 'bg-warning'
  if (responseTime > 500) return 'bg-info'
  return 'bg-success'
}

const getPerformanceText = (responseTime) => {
  if (responseTime < 100) return '极快'
  if (responseTime < 500) return '快速'
  if (responseTime < 1000) return '正常'
  if (responseTime < 3000) return '较慢'
  return '慢'
}

const viewDetails = async (history) => {
  try {
    // 调用API获取详细信息
    const response = await api.get(`/api/v1/dashboard/call-history/${history.id}`)
    const data = response.data

    if (data.success) {
      selectedHistory.value = data.data

      // 解析文件信息
      fileList.value = parseFileInfo(data.data)

      // 使用Bootstrap模态框
      nextTick(() => {
        if (detailModal.value && typeof bootstrap !== 'undefined') {
          const modal = new bootstrap.Modal(detailModal.value)
          modal.show()
        }
      })
    } else {
      toast.error(data.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取调用历史详情失败:', error)
    if (error.response?.status === 401) {
      toast.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/auth/login')
    } else {
      toast.error('获取详情失败，请稍后重试')
    }
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadHistoryData()
}

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadHistoryData()
  }
}

const changePageSize = () => {
  currentPage.value = 1
  loadHistoryData()
}

const loadHistoryData = async () => {
  try {
    // 检查用户是否已登录
    if (!authStore.isAuthenticated) {
      toast.error('请先登录')
      router.push('/auth/login')
      return
    }

    loading.value = true

    // 构建查询参数
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      agentName: searchForm.agentName || undefined,
      callType: searchForm.callType || undefined,
      callStatus: searchForm.callStatus || undefined
    }

    // 调用真实的API
    const response = await api.get('/api/v1/dashboard/call-history', { params })
    const data = response.data

    if (data.success) {
      historyRecords.value = data.data.records || []
      totalRecords.value = data.data.total || 0

      // 更新统计数据
      stats.total = data.data.total || 0
      stats.success = data.data.successCount || 0
      stats.failed = data.data.failedCount || 0
      stats.successRate = data.data.total > 0 ?
        Math.round((data.data.successCount || 0) * 100 / data.data.total) : 0

      console.log('调用历史数据加载成功:', historyRecords.value)
      animateStats()
    } else {
      console.error('加载调用历史失败:', data.message)
      toast.error(data.message || '加载调用历史失败')
      historyRecords.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('加载调用历史失败:', error)
    if (error.response?.status === 401) {
      toast.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/auth/login')
    } else {
      toast.error('网络错误，请稍后重试')
      historyRecords.value = []
      totalRecords.value = 0
    }
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  searchForm.agentName = ''
  searchForm.callType = ''
  searchForm.callStatus = ''
  currentPage.value = 1
  loadHistoryData()
}

// 文件处理相关方法
const parseFileInfo = (history) => {
  const files = []
  const apiBaseUrl = getApiBaseUrl()

  try {
    // 解析上传文件信息
    if (history.uploadFileInfo) {
      const fileInfo = JSON.parse(history.uploadFileInfo)
      if (Array.isArray(fileInfo)) {
        files.push(...fileInfo)
      } else if (fileInfo.name || fileInfo.type) {
        files.push(fileInfo)
      }
    }

    // 解析文件URL列表，转换为预览URL格式
    if (history.uploadFileUrls) {
      const fileUrls = JSON.parse(history.uploadFileUrls)
      if (Array.isArray(fileUrls)) {
        fileUrls.forEach((url, index) => {
          // 从URL中提取文件ID
          const fileId = extractFileIdFromUrl(url)

          // 构建预览URL
          const previewUrl = fileId ? `${apiBaseUrl}/api/v1/files/public/${fileId}/preview` : url

          if (files[index]) {
            files[index].url = previewUrl
            files[index].fileId = fileId
          } else {
            files.push({
              name: `文件${index + 1}`,
              url: previewUrl,
              fileId: fileId,
              type: getFileTypeFromUrl(url)
            })
          }
        })
      }
    }
  } catch (e) {
    console.error('解析文件信息失败:', e)
  }

  return files
}

// 从URL中提取文件ID
const extractFileIdFromUrl = (url) => {
  if (!url) return null

  // 匹配各种可能的URL格式中的文件ID
  const patterns = [
    /\/files\/(\d+)\/preview/,           // /api/v1/files/123/preview
    /\/files\/public\/(\d+)\/preview/,   // /api/v1/files/public/123/preview
    /\/files\/(\d+)/,                    // /api/v1/files/123
    /fileId[=:](\d+)/,                   // fileId=123 或 fileId:123
    /id[=:](\d+)/                        // id=123 或 id:123
  ]

  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }

  return null
}

// 从URL推断文件类型
const getFileTypeFromUrl = (url) => {
  if (!url) return 'unknown'
  const extension = url.split('.').pop()?.toLowerCase()

  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf']

  if (imageTypes.includes(extension)) {
    return 'image'
  } else if (extension === 'pdf') {
    return 'pdf'
  } else if (documentTypes.includes(extension)) {
    return 'document'
  }

  return extension || 'unknown'
}

// 获取内容类型文本
const getContentTypeText = (type) => {
  switch (type) {
    case 'TEXT': return '纯文本'
    case 'PDF': return 'PDF文档'
    case 'IMAGE': return '图片'
    case 'MIXED': return '混合内容'
    default: return type || '未知'
  }
}

// 获取内容类型图标
const getContentTypeIcon = (type) => {
  switch (type) {
    case 'TEXT': return 'fas fa-file-alt'
    case 'PDF': return 'fas fa-file-pdf'
    case 'IMAGE': return 'fas fa-file-image'
    case 'MIXED': return 'fas fa-files'
    default: return 'fas fa-file'
  }
}

// 获取内容类型徽章样式
const getContentTypeBadgeClass = (type) => {
  switch (type) {
    case 'TEXT': return 'badge bg-info'
    case 'PDF': return 'badge bg-danger'
    case 'IMAGE': return 'badge bg-success'
    case 'MIXED': return 'badge bg-warning'
    default: return 'badge bg-secondary'
  }
}

// 获取任务类型文本
const getTaskTypeText = (type) => {
  switch (type) {
    case 'EXTRACT': return '信息提取'
    case 'ANALYZE': return '内容分析'
    case 'TRANSLATE': return '翻译'
    case 'SUMMARIZE': return '摘要'
    case 'OCR': return '文字识别'
    case 'CLASSIFY': return '分类'
    default: return type || '未知'
  }
}

// 获取置信度徽章样式
const getConfidenceBadgeClass = (score) => {
  if (score >= 0.9) return 'badge bg-success'
  if (score >= 0.7) return 'badge bg-warning'
  return 'badge bg-danger'
}

// 获取文件图标
const getFileIcon = (type) => {
  switch (type) {
    case 'pdf': return 'fas fa-file-pdf text-danger'
    case 'image': return 'fas fa-file-image text-success'
    case 'document': return 'fas fa-file-word text-primary'
    case 'text': return 'fas fa-file-alt text-info'
    default: return 'fas fa-file text-muted'
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '未知'
  if (bytes < 1024) return bytes + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
}

// 判断是否可以预览
const canPreview = (type) => {
  return ['pdf', 'image', 'text'].includes(type) || isImageType(type)
}

// 判断是否为图片类型
const isImageType = (type) => {
  return ['image', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type?.toLowerCase())
}

// 判断是否为文本类型
const isTextType = (type) => {
  return ['text', 'txt', 'json', 'xml', 'csv'].includes(type?.toLowerCase())
}

// 格式化JSON字符串
const formatJsonString = (jsonStr) => {
  if (!jsonStr) return ''
  try {
    const obj = JSON.parse(jsonStr)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return jsonStr
  }
}

// 文件预览方法
const previewFileMethod = async (file, index) => {
  try {
    previewFile.value = {
      name: file.name || `文件${index + 1}`,
      type: file.type || 'unknown',
      url: file.url || '',
      content: ''
    }

    // 如果是文本类型，尝试加载内容
    if (isTextType(file.type) && file.url) {
      try {
        const response = await fetch(file.url)
        const text = await response.text()
        previewFile.value.content = text
      } catch (e) {
        console.error('加载文本内容失败:', e)
        previewFile.value.content = '加载失败'
      }
    }

    // 显示预览模态框
    nextTick(() => {
      if (filePreviewModal.value && typeof bootstrap !== 'undefined') {
        const modal = new bootstrap.Modal(filePreviewModal.value)
        modal.show()
      }
    })
  } catch (error) {
    console.error('预览文件失败:', error)
    toast.error('预览文件失败')
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.error('图片加载失败:', event.target.src)
  event.target.style.display = 'none'

  // 显示错误提示
  const errorDiv = document.createElement('div')
  errorDiv.className = 'text-center p-3'
  errorDiv.innerHTML = `
    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
    <p class="text-muted">图片加载失败</p>
  `
  event.target.parentNode.appendChild(errorDiv)
}

// PDF预览相关方法
const openPdfInNewWindow = () => {
  if (previewFile.value.url) {
    // 在新窗口中打开PDF
    const newWindow = window.open(previewFile.value.url, '_blank')
    if (!newWindow) {
      toast.error('无法打开新窗口，请检查浏览器的弹窗拦截设置')
    } else {
      toast.success('PDF已在新窗口中打开')
    }
  } else {
    toast.error('PDF文件链接无效')
  }
}

const copyPdfUrl = async () => {
  if (previewFile.value.url) {
    try {
      await navigator.clipboard.writeText(previewFile.value.url)
      toast.success('PDF链接已复制到剪贴板')
    } catch (err) {
      // 降级到传统方式
      try {
        const textArea = document.createElement('textarea')
        textArea.value = previewFile.value.url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        toast.success('PDF链接已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        toast.error('复制失败，请手动复制链接')
      }
    }
  } else {
    toast.error('PDF文件链接无效')
  }
}

const animateStats = () => {
  const targetStats = stats

  // 重置动画数据
  animatedStats.total = 0
  animatedStats.success = 0
  animatedStats.failed = 0
  animatedStats.successRate = 0

  // 动画函数
  const animateNumber = (key, target, duration = 1500) => {
    const increment = target / (duration / 16)
    let current = 0

    const timer = setInterval(() => {
      current += increment
      if (current >= target) {
        current = target
        clearInterval(timer)
      }
      animatedStats[key] = Math.floor(current)
    }, 16)
  }

  // 延迟启动动画，创建波浪效果
  setTimeout(() => animateNumber('total', targetStats.total), 0)
  setTimeout(() => animateNumber('success', targetStats.success), 200)
  setTimeout(() => animateNumber('failed', targetStats.failed), 400)
  setTimeout(() => animateNumber('successRate', targetStats.successRate), 600)
}

// 生命周期
onMounted(() => {
  // 从localStorage恢复视图设置
  const savedView = localStorage.getItem('historyView') || 'table'
  currentView.value = savedView

  // 加载历史数据
  loadHistoryData()
})
</script>

<style scoped>
/* 调用历史页面样式 - 与HTML版本保持一致 */
.history-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px); /* 减去Header和Footer的大概高度 */
}

.history-row {
  transition: all 0.2s ease;
}

.history-row:hover {
  background-color: #f8f9fa !important;
  transform: translateX(2px);
}

.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.9rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.badge {
  font-size: 0.75rem;
}

.action-buttons .btn {
  min-width: 32px;
  height: 32px;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* 卡片视图样式 */
.history-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.status-badge {
  font-size: 0.75rem;
}

/* 视图切换按钮样式 */
.view-toggle-btn {
  background-color: #FFFFFF !important;
  border-color: #E4E6EA !important;
  color: #5E6278 !important;
  transition: all 0.3s ease !important;
}

.view-toggle-btn:hover {
  background-color: #F9F9F9 !important;
  border-color: #009EF7 !important;
  color: #009EF7 !important;
}

.view-toggle-btn.active {
  background-color: #009EF7 !important;
  border-color: #009EF7 !important;
  color: #FFFFFF !important;
  box-shadow: none !important;
}

.view-toggle-btn.active:hover {
  background-color: #0084d4 !important;
  border-color: #0084d4 !important;
  color: #FFFFFF !important;
}

/* 统计卡片现代化样式 */
.stat-card-modern {
  background: #ffffff;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.stat-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #28a745, #dc3545, #17a2b8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card-modern:hover::before {
  opacity: 1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  font-family: 'Arial', sans-serif;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 分页样式优化 */
.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-color: #6c757d;
  --bs-pagination-bg: #fff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: #495057;
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #adb5bd;
  --bs-pagination-focus-color: #495057;
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(0, 158, 247, 0.25);
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #009EF7;
  --bs-pagination-active-border-color: #009EF7;
  --bs-pagination-disabled-color: #adb5bd;
  --bs-pagination-disabled-bg: #fff;
  --bs-pagination-disabled-border-color: #dee2e6;
}

.pagination .page-link {
  margin: 0 2px;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease;
  font-weight: 500;
}

.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
  border-radius: 0.375rem !important;
}

.pagination .page-item.active .page-link {
  box-shadow: 0 2px 4px rgba(0, 158, 247, 0.2);
}

/* 分页信息样式 */
.pagination-info {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.pagination-info strong {
  color: #495057;
  font-weight: 600;
}

/* 分页容器样式 */
.pagination-container {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem 1.25rem;
  margin-top: 1rem;
  border: 1px solid #e9ecef;
}

/* 每页显示条数选择器样式 */
.page-size-selector {
  font-size: 0.875rem;
}

.page-size-selector .form-select-sm {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

/* 详情弹框样式 */
.text-orange {
  color: #fd7e14;
}

.bg-orange {
  background-color: #fd7e14;
}

.performance-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  transition: all 0.3s ease;
}

.code-block {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* 模态框样式 */
.modal-content {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: var(--kt-border-radius);
  box-shadow: var(--kt-box-shadow);
}

.modal-header {
  background-color: transparent;
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem 1.5rem 1rem;
}

.modal-title {
  color: var(--kt-text-dark);
  font-weight: 600;
  font-size: 1.125rem;
}

.modal-body {
  padding: 1rem 1.5rem;
  color: var(--kt-text-muted);
}

.modal-footer {
  background-color: transparent;
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem 1.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .pagination-container .d-flex {
    flex-direction: column;
    gap: 1rem !important;
    align-items: flex-start !important;
  }

  .pagination-container .d-flex > div:first-child {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  .page-size-selector {
    align-self: flex-start;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}

/* 查询表单样式统一 */
.search-form .form-control,
.search-form .form-select,
.search-form .btn {
  height: 38px;
  line-height: 1.5;
  font-size: 0.875rem;
}

.search-form .form-control {
  padding: 0.375rem 0.75rem;
}

.search-form .form-select {
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
}

.search-form .btn {
  padding: 0.375rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.search-form .form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

/* 文件预览相关样式 */
.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #009EF7;
}

.file-info h6 {
  color: #495057;
  margin-bottom: 0.25rem;
}

.file-actions .btn {
  border-radius: 20px;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

.preview-container {
  background: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
}

.pdf-preview iframe {
  border-radius: 10px;
}

.pdf-preview-placeholder {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px;
  border: 2px dashed #dee2e6;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.pdf-preview-placeholder i {
  opacity: 0.7;
  margin-bottom: 1rem;
}

.pdf-preview-placeholder h4 {
  color: #495057;
  font-weight: 600;
}

.pdf-preview-placeholder p {
  max-width: 400px;
  line-height: 1.6;
}

.pdf-preview-placeholder .btn {
  min-width: 140px;
}

.image-preview {
  background: #fff;
}

.image-preview img {
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.text-preview {
  background: #fff;
  max-height: 70vh;
  overflow-y: auto;
}

.text-preview pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.unsupported-preview {
  background: #fff;
  color: #6c757d;
}

.unsupported-preview i {
  opacity: 0.5;
}

/* 文件预览模态框样式 */
.modal-xl {
  max-width: 90%;
}

.modal-xl .modal-content {
  height: 90vh;
}

.modal-xl .modal-body {
  padding: 0;
  height: calc(90vh - 120px);
  overflow: hidden;
}

/* 响应结果textarea样式 */
.response-textarea {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  resize: vertical;
  min-height: 200px;
  max-height: 400px;
}

.response-textarea:focus {
  background-color: #fff;
  border-color: #009EF7;
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

/* 响应式文件预览 */
@media (max-width: 768px) {
  .file-actions {
    margin-top: 0.5rem;
  }

  .file-actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .modal-xl {
    max-width: 95%;
  }

  .modal-xl .modal-content {
    height: 85vh;
  }

  .modal-xl .modal-body {
    height: calc(85vh - 120px);
  }

  .response-textarea {
    min-height: 150px;
    max-height: 250px;
  }
}
</style>
