<template>
  <div class="dashboard-page">
    <Header />
    
    <main class="main-content">
      <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2>欢迎回来，{{ authStore.user?.realName || '用户' }}！</h2>
                <p class="text-muted">这是您的 SINAIR-AGENT 仪表盘</p>
              </div>
              <div>
                <span class="badge bg-success">在线</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body text-center py-4">
                <div class="stats-number stats-primary animate-count">{{ animatedStats.subscriptions }}</div>
                <div class="stats-label stats-primary">活跃订阅</div>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body text-center py-4">
                <div class="stats-number stats-success animate-count">{{ animatedStats.monthlyCalls }}</div>
                <div class="stats-label stats-success">本月调用</div>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body text-center py-4">
                <div class="stats-number stats-warning animate-count">{{ formatNumber(animatedStats.remainingCalls) }}</div>
                <div class="stats-label stats-warning">剩余额度</div>
              </div>
            </div>
          </div>

          <div class="col-md-3 mb-3">
            <div class="card stats-card">
              <div class="card-body text-center py-4">
                <div class="stats-number stats-info animate-count">{{ animatedStats.successRate }}%</div>
                <div class="stats-label stats-info">成功率</div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <!-- 最近调用 -->
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-history"></i> 最近调用记录
                </h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Agent名称</th>
                        <th>调用类型</th>
                        <th>状态</th>
                        <th>响应时间</th>
                        <th>调用时间</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="call in recentCalls" :key="call.id" v-if="recentCalls.length > 0">
                        <td>{{ call.agentName }}</td>
                        <td>
                          <span v-if="call.callType === 'API'" class="badge bg-primary">API</span>
                          <span v-else-if="call.callType === 'PLUGIN'" class="badge bg-info">插件</span>
                        </td>
                        <td>
                          <span v-if="call.callStatus === 'SUCCESS'" class="badge bg-success">成功</span>
                          <span v-else-if="call.callStatus === 'FAILED'" class="badge bg-danger">失败</span>
                        </td>
                        <td>
                          <span v-if="call.responseTime == null" class="badge bg-secondary">-</span>
                          <span v-else-if="call.responseTime < 1000" class="badge bg-success">{{ call.responseTime }}ms</span>
                          <span v-else-if="call.responseTime < 3000" class="badge bg-warning">{{ call.responseTime }}ms</span>
                          <span v-else class="badge bg-danger">{{ call.responseTime }}ms</span>
                        </td>
                        <td>{{ formatDate(call.createTime || call.createdTime, 'YYYY-MM-DD HH:mm:ss') }}</td>
                      </tr>
                      <tr v-if="recentCalls.length === 0">
                        <td colspan="5" class="text-center text-muted">暂无调用记录</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="text-center">
                  <router-link to="/dashboard/history" class="btn btn-outline-primary">查看全部历史</router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-bolt"></i> 快速操作
                </h5>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <router-link to="/dashboard/subscriptions" class="btn btn-primary">
                    <i class="fas fa-star"></i> 管理订阅
                  </router-link>
                  <router-link to="/api-docs" class="btn btn-outline-primary">
                    <i class="fas fa-book"></i> API文档
                  </router-link>
                  <a href="/api/v1/dashboard/download-plugin?filename=sinoair-agent-chrome-plugin.zip" class="btn btn-outline-info">
                    <i class="fas fa-download"></i> 下载插件
                  </a>
                </div>
              </div>
            </div>

            <!-- 账户信息 -->
            <div class="card mt-3">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-user"></i> 账户信息
                </h5>
              </div>
              <div class="card-body">
                <p><strong>邮箱：</strong>{{ authStore.user?.email || '<EMAIL>' }}</p>
                <p><strong>注册时间：</strong>{{ formatDate(authStore.user?.createdTime || authStore.user?.createTime, 'YYYY-MM-DD') || '-' }}</p>
                <p><strong>最后登录：</strong>{{ authStore.user?.lastLoginTime ? formatDate(authStore.user.lastLoginTime, 'YYYY-MM-DD HH:mm') : '-' }}</p>
              </div>
            </div>
          </div>
        </div>


      </div>
    </main>

    <Footer />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import dayjs from 'dayjs'
import api from '@/utils/api'

const authStore = useAuthStore()

// 响应式数据
const recentCalls = ref([])

const animatedStats = reactive({
  subscriptions: 0,
  monthlyCalls: 0,
  remainingCalls: 0,
  successRate: 0
})

// 方法
const formatDate = (dateString, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!dateString) return '-'

  try {
    // 处理各种可能的时间格式
    const date = dayjs(dateString)
    if (!date.isValid()) {
      console.warn('Invalid date:', dateString)
      return '-'
    }
    return date.format(format)
  } catch (error) {
    console.error('Date formatting error:', error, dateString)
    return '-'
  }
}

const formatNumber = (num) => {
  if (num >= 1000) {
    return num.toLocaleString()
  }
  return num.toString()
}

const getCallTypeClass = (callType) => {
  return callType === 'API' ? 'bg-primary' : 'bg-info'
}

const getCallStatusClass = (callStatus) => {
  return callStatus === 'SUCCESS' ? 'bg-success' : 'bg-danger'
}

const getResponseTimeClass = (responseTime) => {
  if (responseTime == null) return 'bg-secondary'
  if (responseTime < 1000) return 'bg-success'
  if (responseTime < 3000) return 'bg-warning'
  return 'bg-danger'
}

const loadDashboardData = async () => {
  try {
    // 获取仪表盘统计数据
    const statsResponse = await api.get('/api/v1/dashboard/stats')

    if (statsResponse.data.success) {
      const stats = statsResponse.data.data.stats
      // 动画效果更新统计数据
      animateNumber('subscriptions', stats.subscriptions)
      animateNumber('monthlyCalls', stats.monthlyCalls)
      animateNumber('remainingCalls', stats.remainingCalls)
      animateNumber('successRate', stats.successRate)
    }

    // 获取最近调用记录
    const callsResponse = await api.get('/api/v1/dashboard/recent-calls', {
      params: { limit: 5 }
    })

    if (callsResponse.data.success) {
      recentCalls.value = callsResponse.data.data
    }

  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    // 使用模拟数据作为降级方案
    const mockData = {
      subscriptionCount: 5,
      monthlyCalls: 128,
      totalRemainingCalls: 9872,
      successRate: 95.2,
      recentCalls: [
        {
          id: 1,
          agentName: 'SINAIR-AGENT',
          callType: 'API',
          callStatus: 'SUCCESS',
          responseTime: 150,
          createTime: '2024-01-15T10:30:00'
        },
        {
          id: 2,
          agentName: '智能文档分析',
          callType: 'API',
          callStatus: 'SUCCESS',
          responseTime: 850,
          createTime: '2024-01-15T09:45:00'
        },
        {
          id: 3,
          agentName: '数据可视化专家',
          callType: 'PLUGIN',
          callStatus: 'FAILED',
          responseTime: 5000,
          createTime: '2024-01-15T08:20:00'
        },
        {
          id: 4,
          agentName: '代码审查助手',
          callType: 'API',
          callStatus: 'SUCCESS',
          responseTime: 320,
          createTime: '2024-01-14T16:15:00'
        },
        {
          id: 5,
          agentName: '智能翻译助手',
          callType: 'API',
          callStatus: 'SUCCESS',
          responseTime: 680,
          createTime: '2024-01-14T14:30:00'
        }
      ]
    }

    // 设置最近调用数据（降级方案）
    if (recentCalls.value.length === 0) {
      recentCalls.value = mockData.recentCalls
    }

    // 延迟启动动画，让页面先渲染完成（降级方案）
    setTimeout(() => {
      if (animatedStats.subscriptions === 0) {
        animateNumber('subscriptions', mockData.subscriptionCount)
        setTimeout(() => animateNumber('monthlyCalls', mockData.monthlyCalls), 200)
        setTimeout(() => animateNumber('remainingCalls', mockData.totalRemainingCalls), 400)
        setTimeout(() => animateNumber('successRate', mockData.successRate), 600)
      }
    }, 300)

  }
}

const animateNumber = (key, target) => {
  const duration = 1500
  const increment = target / (duration / 16)
  let current = 0

  const timer = setInterval(() => {
    current += increment
    if (current >= target) {
      current = target
      clearInterval(timer)
    }

    // 处理百分比
    if (key === 'successRate') {
      animatedStats[key] = Math.floor(current * 10) / 10 // 保留一位小数
    } else {
      animatedStats[key] = Math.floor(current)
    }
  }, 16)
}

// 生命周期
onMounted(async () => {
  await authStore.initUser()
  loadDashboardData()
})
</script>

<style scoped>
/* 仪表盘页面样式 - 与HTML版本保持一致 */
.dashboard-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

/* 下载插件按钮hover效果 - 白色文字 */
.btn-outline-info:hover {
  color: #fff !important;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

/* 统计卡片样式 */
.stats-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stats-label {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0;
  opacity: 0.8;
}

.stats-primary { color: #007bff; }
.stats-success { color: #28a745; }
.stats-warning { color: #ffc107; }
.stats-info { color: #17a2b8; }

/* 数字增长动画 */
@keyframes countUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-count {
  animation: countUp 0.8s ease-out;
}

/* 表格样式 */
.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

/* 卡片样式 */
.card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

/* 按钮样式 */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

/* 徽章样式 */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-number {
    font-size: 2rem;
  }

  .stats-label {
    font-size: 0.8rem;
  }
}
</style>
