<template>
  <div class="home-page">
    <Header />
    
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="hero-content">
              <h1 class="display-4 fw-bold mb-4 text-white">
                智能文档识别与自动填充平台
              </h1>
              <p class="lead mb-4 text-white-50">
                智能体矩阵基于先进大语言模型技术，实现文档智能识别和表单自动填充，让繁琐的数据录入工作变得简单高效。
              </p>
              <div class="d-flex flex-wrap gap-3 mb-5">
                <button style="background-color: rgb(255, 255, 255);"
                  class="btn btn-light btn-lg px-4 py-3 rounded-pill"
                  @click="handleStartClick"
                >
                  <i class="fas fa-rocket me-2"></i>
                  立即开始
                </button>
              </div>

              <!-- 统计数据 -->
              <div class="hero-stats d-flex flex-wrap gap-4">
                <div class="stat-item">
                  <div class="stat-number h4 mb-0 text-white">98%+</div>
                  <div class="stat-label small text-white-50">识别准确率</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number h4 mb-0 text-white">80%</div>
                  <div class="stat-label small text-white-50">效率提升</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number h4 mb-0 text-white">100万+</div>
                  <div class="stat-label small text-white-50">文档处理量</div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="hero-image text-center">
              <div class="process-flow-enhanced">
                <!-- 流程步骤卡片 -->
                <div class="row g-3 mb-4">
                  <div class="col-4">
                    <div class="process-card" data-step="1">
                      <div class="process-card-inner">
                        <div class="process-icon-wrapper">
                          <i class="fas fa-eye process-icon" title="智能识别"></i>
                          <div class="process-pulse"></div>
                        </div>
                        <div class="process-label">
                          <span class="step-number">01</span>
                          <span class="step-title">智能识别</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-4">
                    <div class="process-card" data-step="2">
                      <div class="process-card-inner">
                        <div class="process-icon-wrapper">
                          <i class="fas fa-brain process-icon" title="AI处理"></i>
                          <div class="process-pulse"></div>
                        </div>
                        <div class="process-label">
                          <span class="step-number">02</span>
                          <span class="step-title">AI处理</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-4">
                    <div class="process-card" data-step="3">
                      <div class="process-card-inner">
                        <div class="process-icon-wrapper">
                          <i class="fas fa-wand-magic process-icon" title="自动填充"></i>
                          <div class="process-pulse"></div>
                        </div>
                        <div class="process-label">
                          <span class="step-number">03</span>
                          <span class="step-title">自动填充</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>



                <!-- 流程描述 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心功能介绍区域 -->
    <section class="py-5 bg-white">
      <div class="container">
        <div class="row text-center mb-5">
          <div class="col-12">
            <h2 class="display-5 fw-bold text-dark mb-4">智能文档识别与自动填充</h2>
            <p class="lead text-muted">
              基于先进的大语言模型技术，实现文档智能识别和表单自动填充，让繁琐的数据录入工作变得简单高效
            </p>
          </div>
        </div>

        <div class="row g-4 mb-5">
          <!-- 功能特色1 -->
          <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <div class="mb-4">
                  <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-eye fa-2x text-white"></i>
                  </div>
                </div>
                <h5 class="card-title fw-bold mb-3">智能文档识别</h5>
                <p class="card-text text-muted">
                  支持PDF、图片、扫描件等多种格式文档的智能识别，准确提取关键信息，识别率高达98%以上
                </p>
              </div>
            </div>
          </div>

          <!-- 功能特色2 -->
          <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <div class="mb-4">
                  <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-wand-magic fa-2x text-white"></i>
                  </div>
                </div>
                <h5 class="card-title fw-bold mb-3">自动表单填充</h5>
                <p class="card-text text-muted">
                  基于大模型理解能力，智能匹配表单字段，自动填充相关信息，大幅提升数据录入效率
                </p>
              </div>
            </div>
          </div>

          <!-- 功能特色3 -->
          <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body text-center p-4">
                <div class="mb-4">
                  <div class="bg-info bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-brain fa-2x text-white"></i>
                  </div>
                </div>
                <h5 class="card-title fw-bold mb-3">大模型驱动</h5>
                <p class="card-text text-muted">
                  集成多种主流大语言模型，包括千问、GPT、DeepSeek等，提供强大的语义理解和推理能力
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景展示 -->
    <section class="py-5 bg-light">
      <div class="container">
        <div class="row text-center mb-5">
          <div class="col-12">
            <h2 class="display-6 fw-bold text-dark mb-4">广泛的应用场景</h2>
            <p class="lead text-muted">
              适用于各行各业的文档处理需求，提升工作效率
            </p>
          </div>
        </div>

        <div class="row g-4">
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm text-center">
              <div class="card-body p-4">
                <div class="mb-3">
                  <i class="fas fa-building fa-3x text-primary"></i>
                </div>
                <h5 class="card-title fw-bold">企业办公</h5>
                <p class="card-text text-muted small">
                  合同管理、发票处理、报表生成等企业日常办公场景
                </p>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm text-center">
              <div class="card-body p-4">
                <div class="mb-3">
                  <i class="fas fa-university fa-3x text-success"></i>
                </div>
                <h5 class="card-title fw-bold">金融服务</h5>
                <p class="card-text text-muted small">
                  身份证件识别、银行流水分析、贷款资料审核等
                </p>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm text-center">
              <div class="card-body p-4">
                <div class="mb-3">
                  <i class="fas fa-hospital fa-3x text-info"></i>
                </div>
                <h5 class="card-title fw-bold">医疗健康</h5>
                <p class="card-text text-muted small">
                  病历信息提取、检查报告解析、医保单据处理等
                </p>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm text-center">
              <div class="card-body p-4">
                <div class="mb-3">
                  <i class="fas fa-graduation-cap fa-3x text-warning"></i>
                </div>
                <h5 class="card-title fw-bold">教育培训</h5>
                <p class="card-text text-muted small">
                  学生信息录入、成绩单处理、证书信息提取等
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <Footer />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据（保持简单）

// 方法
const handleStartClick = () => {
  if (authStore.isAuthenticated) {
    router.push('/agent/square')
  } else {
    router.push('/auth/login')
  }
}
</script>

<style scoped>
.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.hero-section {
  background: var(--kt-gradient-primary);
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-tags .badge {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.hero-image {
  position: relative;
  z-index: 2;
}

.process-flow {
  padding: 2rem;
}

.process-flow .bg-white {
  transition: all 0.3s ease;
}

.process-flow .bg-white:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(255, 255, 255, 0.3);
}

.hero-stats .stat-item {
  text-align: center;
}

.features-section {
  padding: 80px 0;
}

.feature-card {
  transition: all 0.3s ease;
  border: none;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.feature-icon {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-section {
  padding: 60px 0;
}

.stat-item {
  padding: 2rem 1rem;
}

.stat-number {
  font-size: 3rem;
  line-height: 1;
}



.btn-gradient-primary {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  color: var(--kt-primary);
  border: none;
  font-weight: 600;
}

.btn-gradient-primary:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: var(--kt-primary-dark);
  transform: translateY(-2px);
}

/* 深色主题适配 */
.theme-dark .btn-light {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #181c32;
}

.theme-dark .btn-light:hover {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #181c32;
}

/* 增强版流程展示样式 */
.process-flow-enhanced {
  position: relative;
  padding: 2rem 0;
}

.process-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.process-card:hover {
  transform: translateY(-5px);
}

.process-card-inner {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem 1rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.process-card:hover .process-card-inner {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.process-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 0.75rem;
}

.process-icon {
  font-size: 2rem;
  color: #ffffff;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.process-card[data-step="1"] .process-icon {
  color: #4fc3f7;
}

.process-card[data-step="2"] .process-icon {
  color: #66bb6a;
}

.process-card[data-step="3"] .process-icon {
  color: #ffb74d;
}

/* 卡片序列动画 */
.process-card[data-step="1"] {
  animation: cardSequence 4s ease-in-out infinite;
  animation-delay: 0s;
}

.process-card[data-step="2"] {
  animation: cardSequence 4s ease-in-out infinite;
  animation-delay: 1.3s;
}

.process-card[data-step="3"] {
  animation: cardSequence 4s ease-in-out infinite;
  animation-delay: 2.6s;
}

.process-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes cardSequence {
  0%, 75% {
    transform: translateY(0) scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  10%, 15% {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  }
  25% {
    transform: translateY(0) scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}

.process-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.step-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 2rem;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}



.process-description {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.description-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.4);
  }
}

/* 深色主题适配 */
.theme-dark .process-card-inner {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
}

.theme-dark .process-card:hover .process-card-inner {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.theme-dark .description-badge {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .process-flow-enhanced {
    padding: 1rem 0;
  }

  .process-card-inner {
    padding: 1rem 0.5rem;
  }

  .process-icon {
    font-size: 1.5rem;
  }

  .process-pulse {
    width: 45px;
    height: 45px;
  }

  .step-title {
    font-size: 0.75rem;
  }



  .description-badge {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  .process-card-inner {
    padding: 0.75rem 0.25rem;
  }

  .process-icon {
    font-size: 1.25rem;
  }

  .step-number {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }

  .step-title {
    font-size: 0.625rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 100px 0 60px;
  }

  .display-4 {
    font-size: 2rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .features-section {
    padding: 60px 0;
  }
}
</style>
