<template>
  <div class="agreement-page">
    <Header />
    
    <main class="main-content">
      <div class="container py-5">
        <!-- 页面标题 -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="text-center">
              <h1 class="display-5 fw-bold text-primary mb-3">
                <i class="fas fa-clipboard-check me-3"></i>
                使用协议
              </h1>
              <p class="lead text-muted">
                智能体矩阵平台使用协议和规范指南
              </p>
              <div class="text-muted">
                <small>最后更新时间：{{ lastUpdated }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- 协议内容 -->
        <div class="row justify-content-center">
          <div class="col-lg-10 col-xl-8">
            <div class="card shadow-sm">
              <div class="card-body p-5">
                
                <!-- 协议目的 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-bullseye me-2"></i>
                    协议目的
                  </h2>
                  <div class="alert alert-info border-start border-info border-3">
                    <p class="mb-0 lh-lg">
                      本使用协议旨在规范用户在智能体矩阵平台上的行为，确保平台的正常运行和所有用户的良好体验。通过使用我们的服务，您同意遵守本协议的所有条款和条件。
                    </p>
                  </div>
                </section>

                <!-- 账户管理 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-user-circle me-2"></i>
                    账户管理
                  </h2>
                  <div class="row g-4">
                    <div class="col-md-6">
                      <div class="bg-light p-4 rounded h-100">
                        <h5 class="fw-semibold text-success mb-3">
                          <i class="fas fa-check-circle me-2"></i>
                          账户注册
                        </h5>
                        <ul class="text-muted small">
                          <li>提供真实有效的注册信息</li>
                          <li>选择安全的密码并定期更新</li>
                          <li>及时更新账户信息变更</li>
                          <li>一人一账户，禁止多重注册</li>
                        </ul>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="bg-light p-4 rounded h-100">
                        <h5 class="fw-semibold text-warning mb-3">
                          <i class="fas fa-shield-alt me-2"></i>
                          账户安全
                        </h5>
                        <ul class="text-muted small">
                          <li>妥善保管登录凭证</li>
                          <li>不与他人共享账户信息</li>
                          <li>发现异常及时联系客服</li>
                          <li>定期检查账户活动记录</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 数据处理规范 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-database me-2"></i>
                    数据处理规范
                  </h2>
                  <div class="alert alert-warning border-start border-warning border-3">
                    <h6 class="fw-semibold mb-3">数据使用原则：</h6>
                    <div class="row">
                      <div class="col-md-6">
                        <ul class="small text-muted">
                          <li><strong>合法性：</strong>确保上传数据的合法来源</li>
                          <li><strong>准确性：</strong>提供准确完整的数据信息</li>
                          <li><strong>安全性：</strong>不上传包含恶意代码的文件</li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <ul class="small text-muted">
                          <li><strong>隐私性：</strong>保护个人隐私和敏感信息</li>
                          <li><strong>合规性：</strong>遵守相关法律法规要求</li>
                          <li><strong>责任性：</strong>对数据内容承担全部责任</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 违规处理 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-gavel me-2"></i>
                    违规处理
                  </h2>
                  <div class="bg-light p-4 rounded">
                    <p class="fw-semibold mb-3">违规行为的处理措施：</p>
                    <div class="row">
                      <div class="col-md-6">
                        <h6 class="text-warning">轻微违规</h6>
                        <ul class="small text-muted">
                          <li>口头警告</li>
                          <li>限制部分功能</li>
                          <li>要求整改</li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <h6 class="text-danger">严重违规</h6>
                        <ul class="small text-muted">
                          <li>暂停账户</li>
                          <li>永久封禁</li>
                          <li>法律追责</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </section>

                <!-- 协议变更 -->
                <section class="mb-5">
                  <h2 class="h4 text-primary mb-3">
                    <i class="fas fa-edit me-2"></i>
                    协议变更
                  </h2>
                  <p class="text-muted lh-lg">
                    我们保留随时修改本使用协议的权利。重大变更将通过邮件、站内通知或其他适当方式通知用户。继续使用服务即表示您接受修改后的协议。
                  </p>
                </section>



              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <Footer />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

// 最后更新时间
const lastUpdated = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// 设置页面标题
document.title = '使用协议 - 智能体矩阵平台'
</script>

<style scoped>
.agreement-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 140px);
}

.card {
  border: none;
  border-radius: 15px;
}

.border-3 {
  border-width: 3px !important;
}

.lh-lg {
  line-height: 1.8;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  border-color: #dee2e6;
}

/* 深色主题适配 */
.theme-dark .agreement-page {
  background: #1e1e2d;
}

.theme-dark .card {
  background: #2b2b40;
  color: #ffffff;
}

.theme-dark .text-muted {
  color: #a1a5b7 !important;
}

.theme-dark .bg-light {
  background: #3f3f56 !important;
}

.theme-dark .table {
  color: #ffffff;
}

.theme-dark .table th {
  background-color: #3f3f56 !important;
  border-color: #5e6278;
}

.theme-dark .table td {
  border-color: #5e6278;
}

.theme-dark .alert-info {
  background: rgba(0, 158, 247, 0.1) !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}

.theme-dark .alert-warning {
  background: rgba(255, 199, 0, 0.1) !important;
  border-color: #FFC700 !important;
  color: #ffffff;
}

.theme-dark .alert-primary {
  background: rgba(0, 158, 247, 0.1) !important;
  border-color: #009EF7 !important;
  color: #ffffff;
}
</style>
