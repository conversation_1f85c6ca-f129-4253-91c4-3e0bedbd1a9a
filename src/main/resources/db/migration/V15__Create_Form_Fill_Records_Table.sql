-- 创建表单回填记录表
-- V15__Create_Form_Fill_Records_Table.sql

CREATE TABLE form_fill_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '回填记录ID',
    recognition_record_id BIGINT NOT NULL COMMENT '关联的识别记录ID',
    page_binding_id BIGINT NOT NULL COMMENT '页面绑定配置ID',
    target_url VARCHAR(1000) NOT NULL COMMENT '目标页面URL',
    fill_data JSON NOT NULL COMMENT '回填的数据内容',
    fill_result TINYINT NOT NULL DEFAULT 1 COMMENT '回填结果：1-成功，2-部分成功，3-失败',
    error_message TEXT COMMENT '错误信息（回填失败时记录）',
    user_id BIGINT NOT NULL COMMENT '执行回填的用户ID',
    user_feedback TINYINT NULL COMMENT '用户反馈：1-点赞，0-踩，NULL-无反馈',
    feedback_time DATETIME NULL COMMENT '反馈时间',
    feedback_comment VARCHAR(500) NULL COMMENT '反馈备注',
    fill_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '回填时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_recognition_record_id (recognition_record_id),
    INDEX idx_page_binding_id (page_binding_id),
    INDEX idx_user_id (user_id),
    INDEX idx_fill_time (fill_time),
    INDEX idx_user_feedback (user_feedback),
    INDEX idx_fill_result (fill_result),
    INDEX idx_deleted (deleted),
    
    -- 外键约束
    FOREIGN KEY (recognition_record_id) REFERENCES recognition_records(id) ON DELETE CASCADE,
    FOREIGN KEY (page_binding_id) REFERENCES page_bindings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
    
) COMMENT='表单回填记录表';
