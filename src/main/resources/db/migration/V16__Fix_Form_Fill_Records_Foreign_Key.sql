-- 修复表单回填记录表的外键约束问题
-- V16__Fix_Form_Fill_Records_Foreign_Key.sql

-- 删除现有的外键约束
ALTER TABLE form_fill_records DROP FOREIGN KEY form_fill_records_ibfk_1;

-- 修改 recognition_record_id 字段为可空
ALTER TABLE form_fill_records MODIFY COLUMN recognition_record_id BIGINT NULL COMMENT '关联的识别记录ID，NULL表示使用JSON数据';

-- 重新添加外键约束，但只对非空值生效
-- 注意：MySQL的外键约束会自动忽略NULL值
ALTER TABLE form_fill_records 
ADD CONSTRAINT fk_form_fill_records_recognition 
FOREIGN KEY (recognition_record_id) REFERENCES recognition_records(id) ON DELETE CASCADE;
