server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: sinoair-agent

  
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: sinoair-agent
    password: '01JYD8B407AVXFQEEHWJJGEVN2_Agent@sino!@#_'
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

  # Redis配置
  data:
    redis:
      host: *************
      port: 6379
      password: RedisDB.Cloud20211110
      database: 3
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

    # MongoDB配置
    mongodb:
      auto-index-creation: true
      host: *************
      port: 27017
      database: sinoair_agent
      username: sinoair-agent
      password: 'MongoDB.SinoAgent20250625'
      authentication-database: sinoair_agent

  # 邮件配置
  mail:
    host: ************
    port: 6666
    username: <EMAIL>
    password: Sinoair*)$!8907
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: false
          ssl:
            trust: "*"
            enable: false
            checkserveridentity: false
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            fallback: false
            port: 6666
          connectiontimeout: 10000
          timeout: 10000
          writetimeout: 10000
        debug: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# 日志配置
logging:
  level:
    com.sinoair.agent: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
    org.apache.ibatis: DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"

# 应用配置
app:
  # JWT配置
  jwt:
    secret: sinoair-agent-jwt-secret-key-2024-very-long-secure-key-for-hs512-algorithm-minimum-512-bits-required
    expiration: 7200 # 2小时
    refresh-expiration: 604800 # 7天
  
  # 文件存储配置
  file:
    upload-path: ./uploads
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

  # MinIO配置
  minio:
    endpoint: http://*************:9000
    access-key: 'Xm91RPvG3SuT4uhTLaQL'
    secret-key: '1EpsKKJLqQG4dD1KwFcLOP48RDtRzhcbFEmBaLbK'
    bucket-name: 'sinoair-agent'
    region: us-east-1
    # 是否启用MinIO存储，false时使用本地存储
    enabled: true
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    # 写入超时时间（毫秒）
    write-timeout: 60000
    # 读取超时时间（毫秒）
    read-timeout: 10000

  # Chrome扩展更新配置
  extension:
    # 扩展文件存储路径
    update-path: ./extensions
    # 服务器基础URL
    base-url: http://172.17.0.137:8080
    # 当前扩展版本
    current-version: 1.0.0
    # 扩展ID（首次打包后填入）
    extension-id: "abcdefghijklmnopqrstuvwxyzabcdef"
  
  # LLM配置
  llm:
    default-provider: qianwen
    timeout: 30000
    retry-count: 3
    providers:
      zhongwaiyun_llm:
        appKey: OfDFTSK2
        appId: XVpWFydM
        appSecret: 1968311e4ff24786b8b12cd3a0faa328
        base-url: http://172.30.197.3:10000/OcrPlugins/core/ocr
        docType: GENERAL_GLM_EXTRACT
        outputType: ocrResult
      zhongwaiyun_vlm:
        appKey: OfDFTSK2
        appId: XVpWFydM
        appSecret: 1968311e4ff24786b8b12cd3a0faa328
        base-url: http://172.30.197.3:10000/OcrPlugins/core/ocr
        docType: GENERAL_VLM_EXTRACT
        outputType: ocrResult
      openai:
        api-key: ${OPENAI_API_KEY:}
        base-url: https://api.openai.com/v1
        model: gpt-4
      qianwen:
        api-key: ${QIANWEN_API_KEY:sk-test-key}
        base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
        model: qwen-plus
      deepseek:
        api-key: ${DEEPSEEK_API_KEY:}
        base-url: https://api.deepseek.com/v1
        model: deepseek-chat
  # OCR配置
  ocr: # Tesseract配置
      tesseract:
        # Tesseract数据路径，如果为空则使用系统默认路径
        data-path: "C:\\Program Files\\Tesseract-OCR\\tessdata"
        # 支持的语言，多个语言用+连接，如：chi_sim+eng
        language: chi_sim+chi_tra+eng
        # OCR引擎模式：0=仅OCR，1=自动页面分割+OCR，2=自动页面分割但不进行OCR，3=完全自动页面分割但不进行OCR
        oem: 3
        # 页面分割模式：6=统一文本块，7=单一文本行，8=单一单词，10=单一字符，13=原始行，按顺序处理
        psm: 6
        # 图片预处理
        preprocessing:
          # 是否启用图片预处理
          enabled: true
          # 图片缩放因子，用于提高小图片的识别率
          scale-factor: 2.0
          # 是否进行灰度处理
          grayscale: true
          # 是否进行降噪处理
          denoise: true
      # 支持的图片格式
      supported-formats:
        - jpg
        - jpeg
        - png
        - bmp
        - tiff
        - gif
        - webp
      # 最大文件大小（MB）
      max-file-size: 10

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 智能体矩阵平台API文档
    description: 智能文档识别与自动填写平台API接口文档
    version: 1.0.0
    concat: <EMAIL>
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# 邮件功能配置
mail:
  enabled: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true


