<!-- 智能表单配置页面 -->
<style>
    /* CSS变量定义 - 企业级蓝色主题 */
    :root {
        /* 主色调 - 企业友好的蓝色主题 */
        --primary-color: #2563eb;
        --primary-light: #93c5fd;
        --primary-dark: #1d4ed8;
        --secondary-color: #3b82f6;
        --accent-color: #06b6d4;

        /* 状态色彩 */
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #0ea5e9;

        /* 中性色调 */
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --background-color: #f9fafb;
        --card-background: #ffffff;

        /* 间距系统 - 更紧凑 */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;

        /* 圆角系统 */
        --small-radius: 0.375rem;
        --medium-radius: 0.5rem;
        --large-radius: 0.75rem;

        /* 阴影系统 */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-blue: 0 4px 14px 0 rgba(37, 99, 235, 0.15);

        /* 过渡效果 */
        --transition: all 0.2s ease;
        --transition-normal: all 0.3s ease;

        /* 字体系统 - 更紧凑 */
        --font-size-xs: 0.75rem;
        --font-size-sm: 0.875rem;
        --font-size-md: 1rem;
        --font-size-lg: 1.125rem;
        --font-size-xl: 1.25rem;

        /* 智能表单配置专用颜色 - 蓝色主题 */
        --config-header-gradient: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        --config-primary: #2563eb;
        --config-primary-dark: #1d4ed8;
        --border-blue: #dbeafe;
    }

    /* 深色主题 */
    .theme-dark {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        --border-color: #374151;
        --border-light: #4b5563;
        --background-color: #111827;
        --card-background: #1f2937;

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
        --shadow-blue: 0 4px 14px 0 rgba(59, 130, 246, 0.25);

        /* 深色主题下的智能表单配置专用颜色 */
        --config-header-gradient: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
        --config-primary: #3b82f6;
        --config-primary-dark: #2563eb;
        --border-blue: #1e40af;
    }

    /* 主列表页面样式 */
    .config-list-container {
        background: var(--background-color);
        min-height: calc(100vh - 120px);
        padding: var(--spacing-lg);
        transition: var(--transition);
    }

    /* 工具栏样式 */
    .toolbar-card {
        background: var(--card-background);
        border: 1px solid var(--border-light);
        border-radius: var(--small-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--spacing-lg);
    }

    .toolbar-card .card-body {
        padding: var(--spacing-lg);
    }

    /* 搜索区域样式 */
    .search-wrapper {
        min-width: 250px;
        flex: 1;
    }

    .search-input-group .form-control {
        border-right: 0;
        font-size: var(--font-size-sm);
        height: 40px;
        border: 1px solid var(--border-color);
    }

    .search-input-group .input-group-text {
        background: var(--card-background);
        border-left: 0;
        border-right: 0;
        height: 40px;
        border: 1px solid var(--border-color);
        border-right: 0;
    }

    .search-btn {
        font-size: var(--font-size-sm);
        padding: 0.5rem 1rem;
        height: 40px;
        white-space: nowrap;
        margin-left: var(--spacing-sm);
        font-weight: 500;
        border-width: 1px;
        border-style: solid;
    }

    /* 查询按钮特殊样式 - 最高优先级 */
    .search-btn.btn-primary,
    button.search-btn.btn-primary {
        background-color: #2563eb !important;
        border-color: #2563eb !important;
        color: #ffffff !important;
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
        text-shadow: none !important;
        font-weight: 500 !important;
    }

    .search-btn.btn-primary:hover,
    button.search-btn.btn-primary:hover {
        background-color: #1d4ed8 !important;
        border-color: #1d4ed8 !important;
        color: #ffffff !important;
        box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3) !important;
        transform: translateY(-1px);
        text-shadow: none !important;
    }

    /* 重置按钮特殊样式 - 最高优先级 */
    .search-btn.btn-outline-secondary,
    button.search-btn.btn-outline-secondary {
        background-color: #ffffff !important;
        border-color: #d1d5db !important;
        color: #6b7280 !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        text-shadow: none !important;
        font-weight: 500 !important;
    }

    .search-btn.btn-outline-secondary:hover,
    button.search-btn.btn-outline-secondary:hover {
        background-color: #f3f4f6 !important;
        border-color: #2563eb !important;
        color: #2563eb !important;
        box-shadow: 0 2px 6px rgba(37, 99, 235, 0.15) !important;
        transform: translateY(-1px);
        text-shadow: none !important;
    }

    .form-select {
        font-size: var(--font-size-sm);
        height: 40px;
        line-height: 1.5;
        padding: 8px 32px 8px 12px;
        min-width: 140px;
        margin-left: var(--spacing-sm);
        border-color: var(--border-color);
        background-color: var(--card-background);
        color: var(--text-primary);
        transition: var(--theme-transition);
        vertical-align: middle;
    }

    .form-select option {
        padding: 8px 12px;
        line-height: 1.5;
        color: var(--text-primary);
        background-color: var(--card-background);
    }

    /* 工具栏布局优化 */
    .toolbar-row {
        margin-bottom: var(--spacing-md);
    }

    .toolbar-row:last-child {
        margin-bottom: 0;
    }

    /* 内容卡片样式 */
    .content-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--medium-radius);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
    }

    .config-header {
        background: var(--config-header-gradient);
        color: white;
        border-radius: 12px;
        padding: 1.25rem var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--normal-shadow);
    }

    .config-stats {
        display: flex;
        gap: var(--spacing-md);
        margin-top: var(--spacing-md);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 8px;
        padding: 0.75rem;
        text-align: center;
        flex: 1;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.8rem;
        opacity: 0.9;
        line-height: 1;
    }

    /* 搜索和筛选区域 */
    .search-filter-bar {
        background: var(--card-background);
        border-radius: 10px;
        padding: var(--spacing-md) 1.25rem;
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
    }

    .search-input {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem var(--spacing-md);
        font-size: 1rem;
        transition: var(--theme-transition);
        background: var(--card-background);
        color: var(--dark-text);
    }

    .search-input:focus {
        border-color: var(--config-primary);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .filter-btn {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem var(--spacing-lg);
        background: var(--card-background);
        color: var(--dark-text);
        transition: var(--theme-transition);
    }

    .filter-btn:hover, .filter-btn.active {
        border-color: var(--config-primary);
        background: var(--config-primary);
        color: white;
    }

    /* 配置卡片样式 - 企业蓝色主题 */
    .config-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--spacing-lg);
    }

    .config-card {
        background: var(--card-background);
        border: 1px solid var(--border-blue);
        border-radius: var(--medium-radius);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-normal);
        height: 100%;
        overflow: hidden;
        position: relative;
    }

    .config-card:hover {
        box-shadow: var(--shadow-blue);
        transform: translateY(-1px);
        border-color: var(--primary-light);
    }

    .config-card-header {
        background: var(--config-header-gradient);
        border-bottom: 1px solid var(--border-blue);
        padding: var(--spacing-md);
        position: relative;
    }

    .config-type-badge {
        padding: 0.25rem 0.5rem;
        border-radius: var(--small-radius);
        font-size: var(--font-size-xs);
        font-weight: 500;
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
    }

    .config-type-single {
        background: var(--primary-color);
        color: white;
    }

    .config-type-multi {
        background: var(--info-color);
        color: white;
    }

    /* 确保多步骤徽章使用一致的蓝色系 */
    .badge.bg-info,
    .config-type-badge.config-type-multi,
    .badge.config-type-multi-badge {
        background: #0ea5e9 !important;
        color: white !important;
    }

    .config-card-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin: var(--spacing-sm) 0;
        color: var(--text-primary);
        transition: var(--transition);
    }

    .config-card-body {
        padding: var(--spacing-md);
    }

    .config-card-footer {
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--background-color);
        border-top: 1px solid var(--border-light);
    }

    /* 表格样式 - 企业级设计 */
    .configs-table {
        background: var(--card-background);
    }

    .configs-table .table {
        margin-bottom: 0;
        font-size: var(--font-size-sm);
    }

    .configs-table .table thead th {
        background: var(--background-color);
        border-bottom: 2px solid var(--border-light);
        font-weight: 600;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        padding: var(--spacing-md);
        vertical-align: middle;
    }

    .configs-table .table tbody td {
        padding: var(--spacing-md);
        vertical-align: middle;
        border-bottom: 1px solid var(--border-light);
    }

    .configs-table .table tbody tr:hover {
        background: var(--background-color);
    }

    /* 配置信息样式 */
    .config-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .config-avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--small-radius);
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: var(--font-size-sm);
        flex-shrink: 0;
    }

    .config-name {
        font-weight: 600;
        color: var(--text-primary);
        text-decoration: none;
        font-size: var(--font-size-sm);
    }

    .config-name:hover {
        color: var(--primary-color);
    }

    .config-description {
        color: var(--text-muted);
        font-size: var(--font-size-xs);
        margin-top: 2px;
    }

    .config-card-meta {
        display: flex;
        gap: var(--spacing-md);
        margin: var(--spacing-md) 0;
        font-size: 0.9rem;
        color: var(--medium-text);
        transition: var(--theme-transition);
    }

    .config-card-actions {
        display: flex;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-md);
    }

    .action-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: 6px;
        border: none;
        font-size: 0.9rem;
        cursor: pointer;
        transition: var(--theme-transition);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .action-btn-primary {
        background: var(--config-primary);
        color: white;
    }

    .action-btn-primary:hover {
        background: var(--config-primary-dark);
    }

    .action-btn-secondary {
        background: var(--border-color);
        color: var(--medium-text);
        transition: var(--theme-transition);
    }

    .action-btn-secondary:hover {
        background: var(--light-text);
    }

    .theme-dark .action-btn-secondary {
        background: #3a3a52;
        color: var(--medium-text);
    }

    .theme-dark .action-btn-secondary:hover {
        background: #4a4a62;
    }

    .action-btn-danger {
        background: rgba(241, 65, 108, 0.1);
        color: var(--danger-color);
        transition: var(--theme-transition);
    }

    .action-btn-danger:hover {
        background: rgba(241, 65, 108, 0.2);
    }

    /* 状态指示器 */
    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: var(--spacing-sm);
    }

    .status-active {
        background: var(--success-color);
        box-shadow: 0 0 0 2px rgba(80, 205, 137, 0.3);
    }

    .status-inactive {
        background: var(--light-text);
    }

    /* 配置向导样式 */
    .config-wizard {
        background: var(--config-header-gradient);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .wizard-step {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: var(--spacing-md);
    }
    
    .step-number.active {
        background: #ffffff !important; /* 纯白背景 */
        color: #1d4ed8 !important; /* 蓝色数字 */
        border: 3px solid #2563eb !important;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4), 0 0 0 3px rgba(37, 99, 235, 0.1);
        transform: scale(1.05);
        position: relative;
        font-weight: 900 !important; /* 超粗字体 */
    }

    .step-number.completed {
        background: #10b981 !important; /* 纯绿色背景 */
        color: white !important; /* 白色对号 */
        border: 3px solid #059669 !important; /* 深绿色边框 */
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4), 0 0 0 3px rgba(16, 185, 129, 0.1);
        position: relative;
        font-weight: 900 !important; /* 超粗字体 */
    }

    /* 配置类型选择 */
    .config-type-card {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem; /* 减少内边距 */
        text-align: center;
        cursor: pointer;
        transition: var(--theme-transition);
        height: 140px; /* 大幅减少高度 */
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: var(--card-background);
    }

    .config-type-card:hover {
        border-color: var(--primary-color);
        box-shadow: var(--hover-shadow);
        transform: translateY(-2px);
    }

    .config-type-card.selected {
        border-color: var(--primary-color);
        background: rgba(0, 158, 247, 0.05);
    }

    .theme-dark .config-type-card.selected {
        background: rgba(0, 158, 247, 0.1);
    }

    .config-type-icon {
        font-size: 2rem; /* 减少图标大小 */
        margin-bottom: 0.5rem; /* 减少底部间距 */
        color: var(--primary-color);
    }

    /* 步骤配置界面 */
    .step-config-container {
        display: none;
        height: calc(100vh - 200px);
        min-height: 700px;
    }

    .step-config-container.active {
        display: flex;
    }

    .config-panel {
        width: 320px;
        background: var(--background-color);
        border-right: 1px solid var(--border-color);
        padding: var(--spacing-lg);
        overflow-y: auto;
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
    }

    .preview-panel {
        flex: 1;
        padding: var(--spacing-md);
        background: var(--card-background);
        position: relative;
        display: flex;
        flex-direction: column;
        transition: var(--theme-transition);
    }

    .preview-header {
        flex-shrink: 0;
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
        margin-bottom: var(--spacing-md);
        transition: var(--theme-transition);
    }

    .preview-content {
        flex: 1;
        position: relative;
        overflow: hidden;
    }

    .step-progress {
        background: var(--border-color);
        height: 4px;
        border-radius: 2px;
        margin-bottom: var(--spacing-lg);
        transition: var(--theme-transition);
    }

    .step-progress-bar {
        background: var(--primary-color);
        height: 100%;
        border-radius: 2px;
        transition: width 0.3s ease;
    }

    /* 字段绑定模式提示条 */
    .binding-mode {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: var(--config-header-gradient);
        color: white;
        display: none;
        z-index: 15;
        transform: translateY(-100%);
        transition: transform 0.3s ease;
    }

    .binding-mode.active {
        display: block;
        transform: translateY(0);
    }

    .binding-instruction {
        padding: var(--spacing-md) var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--spacing-md);
    }

    .binding-instruction-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex: 1;
    }

    .binding-instruction-text {
        flex: 1;
    }

    .binding-instruction-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-shrink: 0;
    }

    /* 最小化状态 */
    .binding-mode.minimized {
        transform: translateY(-70%);
    }

    .binding-mode.minimized .binding-instruction-content {
        display: none;
    }

    .binding-mode.minimized .binding-instruction {
        padding: var(--spacing-sm) var(--spacing-md);
        justify-content: flex-end;
        background: rgba(102, 126, 234, 0.9);
    }

    .binding-mode.minimized:hover {
        transform: translateY(-50%);
    }

    /* 绑定模式激活时的页面样式 */
    .preview-content.binding-active {
        border: 2px dashed var(--config-primary);
        background: rgba(102, 126, 234, 0.05);
    }

    .theme-dark .preview-content.binding-active {
        background: rgba(90, 103, 216, 0.1);
    }

    /* 高亮绑定元素的样式 */
    .highlight-binding {
        animation: highlightPulse 3s ease-in-out;
        outline: 3px solid var(--warning-color) !important;
        outline-offset: 3px;
        background: rgba(255, 199, 0, 0.2) !important;
    }

    @keyframes highlightPulse {
        0%, 100% {
            outline-color: var(--warning-color);
            background-color: rgba(255, 199, 0, 0.2);
        }
        50% {
            outline-color: #ff6b35;
            background-color: rgba(255, 107, 53, 0.3);
        }
    }

    /* 模板选择样式 - 企业级美化版 */
    .template-card {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        height: 100%;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;
    }

    .template-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .template-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        transform: translateY(-3px);
        background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
    }

    .template-card:hover::before {
        opacity: 1;
    }

    .template-card.selected {
        border-color: #2563eb;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
        transform: translateY(-2px);
    }

    .template-card.selected::before {
        opacity: 1;
    }

    .theme-dark .template-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }

    .theme-dark .template-card:hover {
        background: linear-gradient(135deg, #1f2937 0%, #1e3a8a 100%);
        border-color: #3b82f6;
    }

    .theme-dark .template-card.selected {
        background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
        border-color: #2563eb;
    }

    .template-card .template-icon {
        width: 36px;
        height: 36px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin: 0 auto 0.5rem auto;
    }

    .template-card .template-title {
        font-weight: 600;
        color: var(--dark-text);
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
        transition: var(--theme-transition);
        line-height: 1.3;
    }

    .template-card .template-desc {
        color: var(--medium-text);
        font-size: 0.75rem;
        line-height: 1.3;
        margin-bottom: 0.5rem;
        transition: var(--theme-transition);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .template-card .template-meta {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.7rem;
        color: var(--light-text);
        transition: var(--theme-transition);
    }

    .template-list-container {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: var(--spacing-md);
        background: var(--background-color);
        transition: var(--theme-transition);
    }

    /* 响应式优化 - 适配2K 150%显示 */
    @media (min-width: 1920px) {
        .config-header {
            padding: 1rem 1.25rem;
        }

        .config-stats {
            gap: 0.75rem;
            margin-top: 0.75rem;
        }

        .stat-card {
            padding: 0.5rem;
        }

        .stat-number {
            font-size: 1.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
        }

        .search-filter-bar {
            padding: 0.75rem 1rem;
            margin-bottom: 1.25rem;
        }

        .config-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.25rem;
        }

        .template-card {
            padding: 0.75rem;
        }

        .template-icon {
            width: 40px;
            height: 40px;
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
        }

        .template-title {
            font-size: 0.9rem;
            margin-bottom: 0.375rem;
        }

        .template-desc {
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }
    }

    /* 高分辨率优化 */
    @media (min-width: 2560px) {
        .config-list-container {
            padding: 1.25rem;
        }

        .config-header h1 {
            font-size: 1.75rem;
        }

        .config-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }
    }

    /* 动作模式下的可点击元素样式 */
    .clickable-element {
        outline: 2px dashed #ff6b35 !important;
        outline-offset: 2px;
        background: rgba(255, 107, 53, 0.1) !important;
        cursor: pointer !important;
        transition: var(--theme-transition);
    }

    .clickable-element:hover {
        outline-color: #ff4500 !important;
        background: rgba(255, 69, 0, 0.2) !important;
        transform: scale(1.02);
    }

    .action-target {
        outline: 3px solid var(--success-color) !important;
        outline-offset: 3px;
        background: rgba(80, 205, 137, 0.1) !important;
    }

    .action-indicator {
        position: absolute;
        top: -10px;
        right: -10px;
        background: var(--success-color);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        z-index: 1000;
    }
    
    /* 页面预览 */
    .page-preview {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        background: var(--card-background);
        position: relative;
        overflow: hidden;
        height: 100%;
        box-shadow: var(--normal-shadow);
        transition: var(--theme-transition);
    }

    .page-preview:hover {
        border-color: var(--config-primary);
        box-shadow: var(--hover-shadow);
    }

    .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 10px;
    }

    .preview-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
    }

    .preview-toolbar {
        background: var(--background-color);
        border-bottom: 1px solid var(--border-color);
        padding: 0.75rem var(--spacing-md);
        display: flex;
        justify-content: between;
        align-items: center;
        gap: var(--spacing-md);
        transition: var(--theme-transition);
    }

    .url-input {
        flex: 1;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: var(--spacing-sm) 0.75rem;
        font-size: 0.9rem;
        background: var(--card-background);
        color: var(--dark-text);
        transition: var(--theme-transition);
    }

    .url-input:focus {
        border-color: var(--config-primary);
        outline: none;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }

    /* 改进的按钮样式 */
    .btn-primary {
        background: var(--config-header-gradient);
        border: none;
        transition: var(--theme-transition);
    }

    .btn-primary:hover {
        background: linear-gradient(45deg, var(--config-primary-dark), #6b46c1);
        transform: translateY(-1px);
        box-shadow: var(--hover-shadow);
    }

    /* 加载状态改进 */
    .preview-loading {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(5px);
    }

    .theme-dark .preview-loading {
        background: rgba(30, 30, 45, 0.95);
    }

    /* 字段选择模态框样式 */
    .field-option {
        cursor: pointer;
        transition: var(--theme-transition);
    }

    .field-option:hover {
        background-color: var(--background-color);
        transform: translateX(5px);
    }

    .field-option:hover .bi-arrow-right {
        color: var(--config-primary) !important;
    }

    /* 字段状态样式 */
    .field-item.bound {
        background-color: rgba(80, 205, 137, 0.1);
        border-left: 3px solid var(--success-color);
    }

    .field-item {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin-bottom: var(--spacing-sm);
        transition: var(--theme-transition);
        background: var(--card-background);
    }

    .field-item:hover {
        border-color: var(--config-primary);
        box-shadow: var(--light-shadow);
    }

    /* 字段列表 */
    .field-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 5px;
        background: var(--card-background);
        transition: var(--theme-transition);
    }

    .field-item {
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        transition: var(--theme-transition);
    }

    .field-item:hover {
        background: var(--background-color);
    }

    .field-item.selected {
        background: rgba(0, 158, 247, 0.1);
        border-left: 3px solid var(--primary-color);
    }

    .field-item.bound {
        background: rgba(80, 205, 137, 0.1);
        border-left: 3px solid var(--success-color);
    }

    /* 绑定状态指示 */
    .bound-element {
        outline: 2px solid var(--success-color) !important;
        outline-offset: 2px;
        position: relative;
    }

    .bound-element::after {
        content: '✓';
        position: absolute;
        top: -8px;
        right: -8px;
        background: var(--success-color);
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    /* 动作配置模式 */
    .action-mode .clickable-element {
        cursor: pointer;
        transition: var(--theme-transition);
    }

    .action-mode .clickable-element:hover {
        outline: 2px dashed var(--warning-color);
        outline-offset: 2px;
    }

    .action-target {
        outline: 2px solid var(--warning-color) !important;
        outline-offset: 2px;
        position: relative;
    }

    .action-target::after {
        content: '→';
        position: absolute;
        top: -8px;
        right: -8px;
        background: var(--warning-color);
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }
    
    /* 模板选择卡片 - 紧凑版 */
    .template-card {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        transition: var(--theme-transition);
        height: 140px;
        background: var(--card-background);
        padding: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }

    .template-card:hover {
        border-color: var(--primary-color);
        box-shadow: var(--hover-shadow);
        transform: translateY(-2px);
    }

    .template-card.border-primary {
        border-color: var(--primary-color) !important;
        background-color: rgba(0, 158, 247, 0.05) !important;
    }

    .theme-dark .template-card.border-primary {
        background-color: rgba(0, 158, 247, 0.1) !important;
    }

    /* 终极按钮修复 - 使用最高权重选择器 */
    html body .btn-primary,
    html body button.btn-primary,
    html body .btn.btn-primary,
    html body input[type="button"].btn-primary,
    html body input[type="submit"].btn-primary {
        --bs-btn-bg: #1e40af !important;
        --bs-btn-border-color: #1e40af !important;
        --bs-btn-color: #ffffff !important;
        --bs-btn-hover-bg: #1d4ed8 !important;
        --bs-btn-hover-border-color: #1d4ed8 !important;
        --bs-btn-hover-color: #ffffff !important;
        --bs-btn-focus-bg: #1d4ed8 !important;
        --bs-btn-focus-border-color: #1d4ed8 !important;
        --bs-btn-focus-color: #ffffff !important;
        --bs-btn-active-bg: #1e3a8a !important;
        --bs-btn-active-border-color: #1e3a8a !important;
        --bs-btn-active-color: #ffffff !important;

        background: #1e40af !important;
        background-color: #1e40af !important;
        border: 1px solid #1e40af !important;
        border-color: #1e40af !important;
        color: #ffffff !important;
        font-weight: 600 !important;
        box-shadow: 0 2px 4px rgba(30, 64, 175, 0.3) !important;
        text-shadow: none !important;
    }

    html body .btn-primary:hover,
    html body .btn-primary:focus,
    html body .btn-primary:active,
    html body .btn-primary.active,
    html body button.btn-primary:hover,
    html body button.btn-primary:focus,
    html body button.btn-primary:active,
    html body .btn.btn-primary:hover,
    html body .btn.btn-primary:focus,
    html body .btn.btn-primary:active {
        background: #1d4ed8 !important;
        background-color: #1d4ed8 !important;
        border: 1px solid #1d4ed8 !important;
        border-color: #1d4ed8 !important;
        color: #ffffff !important;
        box-shadow: 0 4px 8px rgba(29, 78, 216, 0.4) !important;
        transform: translateY(-1px);
        text-shadow: none !important;
    }

    .btn-outline-primary {
        color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        background-color: transparent !important;
        font-weight: 500 !important;
    }

    .btn-outline-primary:hover,
    .btn-outline-primary:focus,
    .btn-outline-primary:active,
    .btn-outline-primary.active {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: #ffffff !important;
    }

    .btn-outline-secondary,
    button.btn-outline-secondary,
    .btn.btn-outline-secondary {
        color: #6b7280 !important;
        border-color: #d1d5db !important;
        background-color: #ffffff !important;
        font-weight: 500 !important;
        text-shadow: none !important;
    }

    .btn-outline-secondary:hover,
    .btn-outline-secondary:focus,
    .btn-outline-secondary:active,
    .btn-outline-secondary.active,
    button.btn-outline-secondary:hover,
    button.btn-outline-secondary:focus,
    button.btn-outline-secondary:active,
    .btn.btn-outline-secondary:hover,
    .btn.btn-outline-secondary:focus,
    .btn.btn-outline-secondary:active {
        background-color: #f3f4f6 !important;
        border-color: #2563eb !important;
        color: #2563eb !important;
        text-shadow: none !important;
    }

    /* 按钮组样式 - 最高优先级 */
    .btn-group .btn,
    .btn-group button.btn {
        border-color: #d1d5db !important;
        color: #6b7280 !important;
        background-color: #ffffff !important;
        font-weight: 500 !important;
        text-shadow: none !important;
    }

    .btn-group .btn.active,
    .btn-group button.btn.active {
        background-color: #2563eb !important;
        border-color: #2563eb !important;
        color: #ffffff !important;
        text-shadow: none !important;
    }

    .btn-group .btn:hover:not(.active),
    .btn-group button.btn:hover:not(.active) {
        background-color: #f3f4f6 !important;
        border-color: #2563eb !important;
        color: #2563eb !important;
        text-shadow: none !important;
    }

    /* 确保深色主题下的按钮可见性 */
    .theme-dark .btn-primary {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: #ffffff !important;
    }

    .theme-dark .btn-outline-secondary {
        color: var(--text-secondary) !important;
        border-color: var(--border-color) !important;
        background-color: transparent !important;
    }

    .theme-dark .btn-group .btn {
        background-color: var(--card-background) !important;
        color: var(--text-secondary) !important;
    }

    /* 状态徽章样式 */
    .badge {
        font-size: var(--font-size-xs);
        padding: 0.25rem 0.5rem;
    }

    .badge.bg-primary {
        background-color: var(--primary-color) !important;
    }

    .badge.bg-info {
        background-color: var(--info-color) !important;
    }

    .badge.bg-success {
        background-color: var(--success-color) !important;
    }

    .badge.bg-secondary {
        background-color: var(--text-muted) !important;
        color: white !important;
    }

    /* 业务模板code徽章 - 确保白色文字 */
    .template-card .template-meta .badge.bg-secondary {
        background-color: #6b7280 !important;
        color: white !important;
        font-weight: 500;
    }

    /* 模态框按钮样式修复 */
    .modal .btn-success {
        background-color: var(--success-color) !important;
        border-color: var(--success-color) !important;
        color: white !important;
    }

    .modal .btn-success:hover,
    .modal .btn-success:focus,
    .modal .btn-success:active {
        background-color: #0ea5e9 !important;
        border-color: #0ea5e9 !important;
        color: white !important;
    }

    .modal .btn-outline-info {
        color: var(--info-color) !important;
        border-color: var(--info-color) !important;
        background-color: transparent !important;
    }

    .modal .btn-outline-info:hover,
    .modal .btn-outline-info:focus,
    .modal .btn-outline-info:active {
        background-color: var(--info-color) !important;
        border-color: var(--info-color) !important;
        color: white !important;
    }

    /* 配置向导样式修复 - 企业级蓝色渐变 - 超紧凑版 */
    .config-wizard {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1d4ed8 100%);
        color: white;
        border-radius: 6px;
        padding: 0.5rem 0.75rem; /* 大幅减少内边距 */
        margin-bottom: 1rem; /* 减少底部外边距 */
        box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
        position: relative;
        overflow: hidden;
        min-height: auto;
    }

    .config-wizard::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }

    .wizard-step {
        display: flex;
        align-items: center;
        gap: 0.375rem; /* 减少间距 */
        padding: 0.125rem 0.375rem; /* 大幅减少内边距 */
        border-radius: 3px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .step-number {
        width: 24px; /* 减少尺寸 */
        height: 24px; /* 减少尺寸 */
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 11px; /* 减少字体大小 */
        background: rgba(255, 255, 255, 0.9);
        color: #6b7280;
        border: 2px solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 减少阴影 */
        position: relative;
        flex-shrink: 0;
    }

    .step-number::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
        z-index: -1;
    }

    /* 重复的样式已删除，使用上面的统一样式 */

    .wizard-step .step-info h6 {
        font-size: 11px; /* 减少字体大小 */
        margin-bottom: 0px; /* 移除底部间距 */
        font-weight: 600;
        line-height: 1.1; /* 减少行高 */
        color: white !important;
    }

    .wizard-step .step-info small {
        font-size: 9px; /* 减少字体大小 */
        opacity: 0.85;
        line-height: 1.1; /* 减少行高 */
        color: white !important;
    }

    /* 配置向导内所有文字都是白色 */
    .config-wizard,
    .config-wizard *,
    .config-wizard h1,
    .config-wizard h2,
    .config-wizard h3,
    .config-wizard h4,
    .config-wizard h5,
    .config-wizard h6,
    .config-wizard p,
    .config-wizard span,
    .config-wizard div,
    .config-wizard small {
        color: white !important;
    }

    .config-wizard .step-number {
        color: #6b7280 !important;
        background: rgba(255, 255, 255, 0.9) !important;
    }

    .config-wizard .step-number.active {
        background: #ffffff !important; /* 纯白背景 */
        color: #1d4ed8 !important; /* 蓝色数字 */
        border: 3px solid #2563eb !important;
        font-weight: 900 !important;
    }

    .config-wizard .step-number.completed {
        background: #10b981 !important; /* 纯绿色背景 */
        color: white !important; /* 白色对号 */
        border: 3px solid #059669 !important;
        font-weight: 900 !important;
    }

    /* 确保步骤信息文字是白色 - 针对具体HTML结构 */
    .wizard-step .step-info,
    .wizard-step .step-info *,
    .wizard-step h6,
    .wizard-step small,
    .wizard-step .fw-bold,
    .wizard-step .opacity-75,
    .wizard-step div:not(.step-number) {
        color: white !important;
    }

    /* 针对具体的步骤文字结构 */
    .config-wizard .wizard-step > div:last-child,
    .config-wizard .wizard-step > div:last-child *,
    .config-wizard .wizard-step > div:last-child .fw-bold,
    .config-wizard .wizard-step > div:last-child small {
        color: white !important;
    }

    .theme-dark .config-wizard {
        color: white;
    }

    /* 最强力的白色文字规则 - 覆盖所有可能的样式 */
    .config-wizard .fw-bold,
    .config-wizard .opacity-75,
    .config-wizard small.opacity-75 {
        color: white !important;
        opacity: 1 !important;
    }

    .config-wizard small.opacity-75 {
        opacity: 0.9 !important;
    }

    /* 分页样式 */
    .card-footer {
        background: var(--background-color);
        border-top: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
    }

    /* 精简步骤卡片的内边距 */
    .wizard-step-content .card {
        margin-bottom: 0.75rem; /* 减少卡片间距 */
    }

    .wizard-step-content .card-header {
        padding: 0.75rem 1rem; /* 减少头部内边距 */
    }

    .wizard-step-content .card-body {
        padding: 1rem; /* 减少主体内边距 */
    }

    .wizard-step-content .card-title {
        font-size: 1.1rem; /* 减少标题字体大小 */
        margin-bottom: 0;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .step-config-container {
            flex-direction: column;
        }

        .config-panel {
            width: 100%;
            max-height: 300px;
        }

        .config-type-card {
            height: 150px;
        }

        .template-card {
            height: 150px;
        }

        .config-list-container {
            padding: var(--spacing-sm);
        }

        .toolbar-card .row {
            flex-direction: column;
        }

        .toolbar-card .col,
        .toolbar-card .col-auto {
            margin-bottom: var(--spacing-sm);
        }

        .search-wrapper {
            width: 100%;
        }

        .config-cards-container {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 576px) {
        .toolbar-card .d-flex {
            flex-direction: column;
        }

        .search-btn,
        .form-select {
            width: 100%;
            margin-left: 0 !important;
            margin-top: var(--spacing-sm);
        }

        .btn-group {
            width: 100%;
            margin-top: var(--spacing-sm);
        }

        .btn-group .btn {
            flex: 1;
        }

        .text-end {
            text-align: left !important;
        }

        .justify-content-between {
            flex-direction: column;
            align-items: stretch !important;
        }

        .toolbar-card .col-lg-8,
        .toolbar-card .col-lg-6,
        .toolbar-card .col-lg-4 {
            margin-bottom: var(--spacing-md);
        }
    }
</style>

<!-- 主容器 -->
<div class="config-list-container">
    <!-- 工具栏 -->
    <div class="toolbar-card">
        <div class="card-body py-2">
            <!-- 单行布局：搜索 + 过滤 + 操作 -->
            <div class="row align-items-center">
                <div class="col-lg-4">
                    <div class="d-flex align-items-center">
                        <!-- 搜索框 -->
                        <div class="search-wrapper">
                            <div class="input-group search-input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" class="form-control"
                                       placeholder="搜索配置名称..."
                                       id="configSearchInput">
                            </div>
                        </div>

                        <!-- 搜索按钮 -->
                        <button class="btn btn-primary search-btn" onclick="searchConfigs()" type="button">
                            <i class="bi bi-search me-1"></i>查询
                        </button>

                        <!-- 重置按钮 -->
                        <button class="btn btn-outline-secondary search-btn" onclick="resetSearch()" type="button">
                            <i class="bi bi-arrow-clockwise me-1"></i>重置
                        </button>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="d-flex align-items-center justify-content-center">
                        <!-- 状态过滤 -->
                        <select class="form-select" id="statusFilter" onchange="filterConfigs()" style="margin-left: 0;">
                            <option value="">全部状态</option>
                            <option value="1">已启用</option>
                            <option value="0">已禁用</option>
                        </select>

                        <!-- 类型过滤 -->
                        <select class="form-select" id="typeFilter" onchange="filterConfigs()">
                            <option value="">全部类型</option>
                            <option value="single">单页面</option>
                            <option value="multi">多步骤</option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="text-muted small me-3" id="configStats">
                            共 <span id="totalConfigs">0</span> 个配置
                        </span>

                        <div class="btn-group me-2" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary active"
                                    id="cardViewBtn" onclick="switchView('card')">
                                <i class="bi bi-grid-3x3-gap"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    id="tableViewBtn" onclick="switchView('table')">
                                <i class="bi bi-list"></i>
                            </button>
                        </div>

                        <!-- 新建按钮 -->
                        <button type="button" class="btn btn-primary" onclick="startNewConfig()">
                            <i class="bi bi-plus-circle me-1"></i>新建配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-card">
        <div class="card-body p-0">
            <!-- 卡片视图 -->
            <div id="configCardsView" class="p-3">
                <div class="row g-3" id="configCardsContainer">
                    <!-- 配置卡片将动态加载 -->
                </div>
            </div>

            <!-- 表格视图 -->
            <div id="configTableView" class="configs-table" style="display: none;">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>配置信息</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>业务模板</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="configTableBody">
                        <!-- 表格数据将动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="text-center py-5" style="display: none;">
                <i class="bi bi-magic text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3 mb-2">暂无配置</h5>
                <p class="text-muted mb-4">您还没有创建任何智能表单配置，点击下方按钮开始创建</p>
                <button type="button" class="btn btn-primary" onclick="startNewConfig()">
                    <i class="bi bi-plus-circle me-2"></i>创建第一个配置
                </button>
            </div>
        </div>

        <!-- 分页 -->
        <div class="card-footer bg-light py-2">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small" id="paginationInfo">
                    显示第 1-10 条，共 0 条记录
                </div>
                <div id="paginationContainer">
                    <!-- 分页控件将在这里渲染 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置向导模态框 -->
<div class="modal fade" id="configWizardModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header" style="background: var(--config-header-gradient); color: var(--text-primary); border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title" style="color: var(--text-primary); font-weight: 600;">
                    <i class="bi bi-magic me-2"></i>智能表单配置向导
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" onclick="exitWizard()"
                        aria-label="关闭"></button>
            </div>
            <div class="modal-body p-0">
                <!-- 配置向导进度条 -->
                <div class="config-wizard">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="wizard-step">
                                <div class="step-number active" id="step1Number">1</div>
                                <div>
                                    <div class="fw-bold" style="color: white !important;">选择模板</div>
                                    <small class="opacity-75" style="color: white !important; opacity: 0.9 !important;">选择业务模板</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="wizard-step">
                                <div class="step-number" id="step2Number">2</div>
                                <div>
                                    <div class="fw-bold" style="color: white !important;">配置类型</div>
                                    <small class="opacity-75" style="color: white !important; opacity: 0.9 !important;">单步骤或多步骤</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="wizard-step">
                                <div class="step-number" id="step3Number">3</div>
                                <div>
                                    <div class="fw-bold" style="color: white !important;">可视化配置</div>
                                    <small class="opacity-75" style="color: white !important; opacity: 0.9 !important;">绑定字段和动作</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="wizard-step">
                                <div class="step-number" id="step4Number">4</div>
                                <div>
                                    <div class="fw-bold" style="color: white !important;">完成配置</div>
                                    <small class="opacity-75" style="color: white !important; opacity: 0.9 !important;">保存并测试</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

<!-- 步骤1: 选择业务模板 -->
<div id="step1" class="wizard-step-content">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-file-earmark-text me-2"></i>选择业务模板
            </h5>
        </div>
        <div class="card-body">
            <!-- 模板搜索和筛选 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" id="templateSearchInput"
                               placeholder="搜索模板名称或描述..."
                               onkeyup="filterTemplates()">
                    </div>
                </div>
                <div class="col-md-6">
                    <select class="form-select" id="templateCategoryFilter" onchange="filterTemplates()">
                        <option value="">所有分类</option>
                        <option value="form">表单类</option>
                        <option value="ecommerce">电商类</option>
                        <option value="registration">注册类</option>
                        <option value="survey">调查类</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>

            <!-- 模板列表 -->
            <div class="template-list-container" style="max-height: 400px; overflow-y: auto;">
                <div class="row g-3" id="templateSelection">
                    <!-- 模板选项将动态加载 -->
                </div>
                <div id="noTemplatesFound" class="text-center text-muted py-4" style="display: none;">
                    <i class="bi bi-search"></i>
                    <p class="mb-0">未找到匹配的模板</p>
                </div>
            </div>

            <!-- 已选择模板信息 -->
            <div id="selectedTemplateInfo" class="alert alert-info mt-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <div>
                        <strong>已选择：</strong><span id="selectedTemplateName"></span>
                        <br><small id="selectedTemplateDesc"></small>
                    </div>
                </div>
            </div>

            <div class="mt-4 text-end">
                <button type="button" class="btn btn-primary" onclick="nextStep()" id="step1NextBtn" disabled>
                    下一步 <i class="bi bi-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 步骤2: 选择配置类型 -->
<div id="step2" class="wizard-step-content" style="display: none;">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-gear me-2"></i>选择配置类型
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="config-type-card" onclick="selectConfigType('single')" id="singleTypeCard">
                        <div class="config-type-icon">
                            <i class="bi bi-file-earmark"></i>
                        </div>
                        <h5>单页面表单</h5>
                        <p class="text-muted mb-0">适用于简单的单页面表单填充</p>
                        <small class="text-success mt-2">✓ 配置简单 ✓ 快速上手</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="config-type-card" onclick="selectConfigType('multi')" id="multiTypeCard">
                        <div class="config-type-icon">
                            <i class="bi bi-diagram-3"></i>
                        </div>
                        <h5>多步骤表单</h5>
                        <p class="text-muted mb-0">适用于复杂的多步骤表单流程</p>
                        <small class="text-info mt-2">✓ 功能强大 ✓ 支持复杂流程</small>
                    </div>
                </div>
            </div>
            <div class="mt-4 d-flex justify-content-between">
                <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                    <i class="bi bi-arrow-left"></i> 上一步
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep()" id="step2NextBtn" disabled>
                    下一步 <i class="bi bi-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 步骤3: 可视化配置 -->
<div id="step3" class="wizard-step-content" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-magic me-2"></i>可视化配置
            </h5>
            <div class="d-flex align-items-center gap-3">
                <div class="step-progress" style="width: 200px;">
                    <div class="step-progress-bar" id="configProgress" style="width: 0%;"></div>
                </div>
                <span class="text-muted" id="progressText">步骤 1 / 1</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="step-config-container active" id="stepConfigContainer">
                <!-- 左侧配置面板 -->
                <div class="config-panel">
                    <div id="configPanelContent">
                        <!-- 配置内容将动态加载 -->
                    </div>
                </div>
                
                <!-- 右侧预览面板 -->
                <div class="preview-panel">
                    <!-- 预览头部 -->
                    <div class="preview-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="bi bi-globe me-2"></i>页面预览
                            </h6>
                            <div class="d-flex gap-2">



                                <button type="button" class="btn btn-sm btn-outline-info" onclick="pasteHtmlContent()">
                                    <i class="bi bi-clipboard-plus"></i> 粘贴HTML
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="toggleBindingMode()" id="bindingToggleBtn">
                                    <i class="bi bi-magic"></i> 开始绑定
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 预览内容区域 -->
                    <div class="preview-content">
                        <div class="page-preview" id="pagePreview">
                            <!-- 工具栏 -->
                            <div class="preview-toolbar">
                                <div class="text-center w-100">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        粘贴页面HTML内容，然后开始字段绑定配置
                                    </small>
                                </div>
                            </div>

                            <!-- 页面内容区域 -->
                            <div id="previewContent" style="height: calc(100% - 60px); position: relative;">
                                <div class="text-center py-5 text-muted">
                                    <i class="bi bi-shield-check display-4 mb-3 text-success"></i>
                                    <h5>智能表单配置</h5>
                                    <p class="mb-3">粘贴页面HTML内容开始配置</p>

                                    <div class="d-flex flex-column gap-3 align-items-center">
                                        <div class="alert alert-info">
                                            <i class="bi bi-lightbulb me-2"></i>
                                            <strong>使用说明：</strong><br>
                                            1. 浏览器插件复制目标页面的HTML源码<br>
                                            2. 点击上方粘贴html<br>
                                            3. 开始配置
                                        </div>

                                        <div class="text-muted small">
                                            <i class="bi bi-info-circle me-1"></i>
                                            或者直接粘贴已复制的HTML内容
                                        </div>

                                        <button type="button" class="btn btn-outline-info mt-2" onclick="pasteHtmlContent()">
                                            <i class="bi bi-clipboard-plus me-1"></i>粘贴HTML内容
                                        </button>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 绑定模式提示条 -->
                        <div class="binding-mode" id="bindingMode">
                            <div class="binding-instruction">
                                <div class="binding-instruction-content">
                                    <i class="bi bi-magic text-white" style="font-size: 1.2rem;"></i>
                                    <div class="binding-instruction-text">
                                        <strong>字段绑定模式已激活</strong>
                                        <small class="d-block opacity-75">点击页面中的输入框来绑定JSON字段</small>
                                    </div>
                                </div>
                                <div class="binding-instruction-actions">
                                    <button type="button" class="btn btn-sm btn-outline-light" onclick="showBindingsList()" title="查看已绑定字段">
                                        <i class="bi bi-list-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-light" onclick="showBindingHelp()" title="绑定帮助">
                                        <i class="bi bi-question-circle"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-light" onclick="minimizeBindingMode()" title="最小化提示" id="minimizeBindingBtn">
                                        <i class="bi bi-dash"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light" onclick="exitBindingMode()" title="退出绑定模式">
                                        <i class="bi bi-x text-dark"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                    <i class="bi bi-arrow-left"></i> 上一步
                </button>
                <div>
                    <!-- 预览配置按钮已移除 -->
                    <button type="button" class="btn btn-primary" onclick="nextStep()">
                        下一步 <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 步骤4: 完成配置 -->
<div id="step4" class="wizard-step-content" style="display: none;">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-check-circle me-2"></i>完成配置
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h6>配置摘要</h6>
                    <div id="configSummary">
                        <!-- 配置摘要将动态生成 -->
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>保存选项</h6>
                    <div class="mb-3">
                        <label class="form-label">配置名称</label>
                        <input type="text" class="form-control" id="configName" placeholder="请输入配置名称">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">URL匹配规则</label>
                        <input type="text" class="form-control" id="urlPattern" placeholder="例如：/user/register 或 *.example.com/form">
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            支持通配符匹配，如：/user/* 或 *.domain.com/path
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">目标页面URL</label>
                        <input type="text" class="form-control" id="targetUrl" placeholder="完整的目标页面URL（可选）">
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            用于测试和验证的完整URL
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" id="configDescription" rows="3" placeholder="可选：描述此配置的用途"></textarea>
                    </div>
                </div>
            </div>
            <div class="mt-4 d-flex justify-content-between">
                <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                    <i class="bi bi-arrow-left"></i> 上一步
                </button>
                <div>
                    <!-- 测试配置按钮已移除 -->
                    <button type="button" class="btn btn-success" onclick="saveConfig()">
                        <i class="bi bi-check"></i> 保存配置
                    </button>
                </div>
            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置列表模态框 -->
<div class="modal fade" id="configListModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">智能表单配置列表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="configListTable">
                        <thead>
                            <tr>
                                <th>配置名称</th>
                                <th>类型</th>
                                <th>业务模板</th>
                                <th>步骤数</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
