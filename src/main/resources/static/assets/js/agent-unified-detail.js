/**
 * Agent统一详情页面控制器
 * 支持查看、审批、补充资料三种模式
 */

// 页面模式枚举
const PAGE_MODES = {
    VIEW: 'view',           // 查看模式
    APPROVAL: 'approval',   // 审批模式
    SUPPLEMENT: 'supplement' // 补充资料模式
};

// 全局变量
let currentAgentId = null;
let currentMode = PAGE_MODES.VIEW;
let agentData = null;
let supplementData = null;
let uploadedFiles = []; // 存储上传的截图文件URL
let selectedVersionId = null; // 选中的版本ID（用于补充资料模式）
let selectedVersionNumber = null; // 选中的版本号（用于补充资料模式）

/**
 * 页面初始化
 */
function initUnifiedDetailPage() {
    console.log('初始化Agent统一详情页面');
    
    try {
        // 获取页面参数
        const params = getPageParams();
        currentAgentId = params.agentId;
        currentMode = params.mode || PAGE_MODES.VIEW;
        selectedVersionId = params.selectedVersionId;
        selectedVersionNumber = params.selectedVersionNumber;
        
        if (!currentAgentId) {
            showToast('缺少Agent ID参数', 'error');
            setTimeout(() => goBackToSourcePage(), 2000);
            return;
        }
        
        console.log('页面参数:', { agentId: currentAgentId, mode: currentMode });
        
        // 初始化页面UI
        initPageUI();
        
        // 初始化Tab事件
        initTabEvents();
        
        // 加载数据
        loadAllData();
        
    } catch (error) {
        console.error('页面初始化失败:', error);
        showToast('页面初始化失败', 'error');
    }
}

/**
 * 获取页面参数
 */
function getPageParams() {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const agentId = urlParams.get('agentId') || getAgentIdFromContext();
    const mode = urlParams.get('mode') || sessionStorage.getItem('agentDetailMode') || PAGE_MODES.VIEW;

    // 获取版本信息（用于补充资料模式）
    const selectedVersionId = sessionStorage.getItem('selectedVersionId');
    const selectedVersionNumber = sessionStorage.getItem('selectedVersionNumber');

    return { agentId, mode, selectedVersionId, selectedVersionNumber };
}

/**
 * 从上下文获取Agent ID（兼容现有代码）
 */
function getAgentIdFromContext() {
    // 尝试从sessionStorage获取
    let agentId = sessionStorage.getItem('currentAgentId');
    
    // 尝试从URL路径获取
    if (!agentId) {
        const pathParts = window.location.pathname.split('/');
        const agentIndex = pathParts.indexOf('agent');
        if (agentIndex !== -1 && pathParts[agentIndex + 1]) {
            agentId = pathParts[agentIndex + 1];
        }
    }
    
    // 尝试从全局变量获取
    if (!agentId && typeof window.currentAgentId !== 'undefined') {
        agentId = window.currentAgentId;
    }
    
    return agentId;
}

/**
 * 初始化页面UI
 */
function initPageUI() {
    // 设置页面标题和操作按钮
    updatePageHeader();

    // 根据模式显示/隐藏相应的操作区域
    updateModeSpecificUI();

    // 更新模式特定的内容显示
    updateModeSpecificContent();

    // 初始化富文本编辑器和文件上传（如果是补充资料模式）
    if (currentMode === PAGE_MODES.SUPPLEMENT) {
        // 延迟初始化富文本编辑器，确保DOM已渲染
        setTimeout(() => {
            initRichTextEditors();
            initFileUpload();
        }, 500);
    }
}

/**
 * 更新页面头部
 */
function updatePageHeader() {
    const titleElement = document.querySelector('.header-actions-title');
    const descElement = document.querySelector('.header-actions-desc');
    const actionsElement = document.querySelector('.header-actions-right');
    
    if (!titleElement || !descElement || !actionsElement) {
        console.warn('页面头部元素未找到');
        return;
    }
    
    // 根据模式设置标题和描述
    switch (currentMode) {
        case PAGE_MODES.VIEW:
            titleElement.innerHTML = '<i class="bi bi-info-circle"></i>Agent详情信息';
            descElement.textContent = '查看Agent的详细信息、使用场景和配置参数';
            actionsElement.innerHTML = `
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshAllData()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
                <button type="button" class="btn btn-outline-dark btn-sm" onclick="goBackToSourcePage()">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </button>
            `;
            break;
            
        case PAGE_MODES.APPROVAL:
            titleElement.innerHTML = '<i class="bi bi-clipboard-check"></i>审批操作';
            descElement.textContent = '请仔细审核Agent信息，填写审批意见后进行审批操作';
            actionsElement.innerHTML = `
                <button type="button" class="btn btn-success btn-sm" id="approveBtn">
                    <i class="bi bi-check-circle"></i> 审批通过
                </button>
                <button type="button" class="btn btn-danger btn-sm" id="rejectBtn">
                    <i class="bi bi-x-circle"></i> 审批不通过
                </button>
                <button type="button" class="btn btn-outline-dark btn-sm" onclick="goBackToSourcePage()">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </button>
            `;
            break;
            
        case PAGE_MODES.SUPPLEMENT:
            let supplementTitle = '<i class="bi bi-gear"></i>操作选项';
            let supplementDesc = '保存草稿可以随时修改，提交后将发送给管理员审核';

            // 如果有选中的版本，显示版本信息
            if (selectedVersionNumber) {
                supplementTitle = `<i class="bi bi-gear"></i>申请发布版本 <span class="badge bg-info text-white ms-2">${selectedVersionNumber}</span>`;
                supplementDesc = `正在为版本 ${selectedVersionNumber} 补充资料，提交后将发送给管理员审核`;
            }

            titleElement.innerHTML = supplementTitle;
            descElement.textContent = supplementDesc;
            actionsElement.innerHTML = `
                <button type="button" class="btn btn-success btn-sm" id="saveBtn">
                    <i class="bi bi-save"></i> 保存草稿
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="submitBtn">
                    <i class="bi bi-check-circle"></i> 提交审核
                </button>
                <button type="button" class="btn btn-outline-dark btn-sm" onclick="goBackToSourcePage()">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </button>
            `;
            break;
    }
}

/**
 * 更新模式特定的UI
 */
function updateModeSpecificUI() {
    // 显示/隐藏浮动操作区域
    const floatingActions = document.querySelector('.floating-actions');
    if (floatingActions) {
        floatingActions.style.display = currentMode === PAGE_MODES.SUPPLEMENT ? 'block' : 'none';
    }
    
    // 绑定模式特定的事件
    bindModeSpecificEvents();
}

/**
 * 绑定模式特定的事件
 */
function bindModeSpecificEvents() {
    // 移除之前的事件监听器
    document.removeEventListener('click', handleButtonClick);
    
    // 添加新的事件监听器
    document.addEventListener('click', handleButtonClick);
}

/**
 * 处理按钮点击事件
 */
function handleButtonClick(event) {
    const target = event.target.closest('button');
    if (!target) return;
    
    const buttonId = target.id;
    
    switch (buttonId) {
        case 'approveBtn':
            handleApprove();
            break;
        case 'rejectBtn':
            handleReject();
            break;
        case 'saveBtn':
            handleSaveDraft();
            break;
        case 'submitBtn':
            handleSubmitForApproval();
            break;
    }
}

/**
 * 初始化Tab切换事件
 */
function initTabEvents() {
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function (event) {
            const targetTab = event.target.getAttribute('data-bs-target');
            console.log('切换到Tab:', targetTab);
            
            // 延迟加载Tab数据，确保Tab切换动画完成
            setTimeout(() => {
                loadTabData(targetTab);
            }, 100);
        });
    });
}

/**
 * 加载所有数据
 */
async function loadAllData() {
    try {
        showPageLoading(true);
        
        // 并行加载基础数据
        const promises = [
            loadAgentBasicInfo(),
            loadSupplementInfo()
        ];
        
        await Promise.all(promises);
        
        // 加载当前激活Tab的数据
        const activeTab = document.querySelector('.nav-link.active');
        if (activeTab) {
            const targetTab = activeTab.getAttribute('data-bs-target');
            await loadTabData(targetTab);
        }
        
        showPageLoading(false);
        console.log('所有数据加载完成');
        
    } catch (error) {
        console.error('数据加载失败:', error);
        showToast('数据加载失败', 'error');
        showPageLoading(false);
    }
}

/**
 * 加载Agent基础信息
 */
async function loadAgentBasicInfo() {
    try {
        console.log('开始加载Agent基础信息');

        let response;
        if (currentMode === PAGE_MODES.APPROVAL) {
            response = await apiCall(`/api/v1/agent-approval/detail/${currentAgentId}`, 'GET');
        } else {
            response = await apiCall(`/api/v1/agents/${currentAgentId}`, 'GET');
        }

        if (response.code === 200) {
            if (currentMode === PAGE_MODES.APPROVAL) {
                // 审批模式：适配AgentApprovalDetailVO数据结构
                const approvalData = response.data;
                agentData = {
                    id: approvalData.basicInfo?.id,
                    agentName: approvalData.basicInfo?.agentName,
                    agentCode: approvalData.basicInfo?.agentCode,
                    categoryName: approvalData.basicInfo?.categoryName,
                    description: approvalData.basicInfo?.description,
                    creatorName: approvalData.basicInfo?.creatorName,
                    createdTime: approvalData.basicInfo?.createTime,
                    version: approvalData.basicInfo?.version,
                    upgradeContent: approvalData.basicInfo?.upgradeContent,
                    status: 1 // 审批中状态
                };

                // 保存完整的审批数据用于其他Tab
                window.approvalDetailData = approvalData;

                // 如果有使用场景数据，设置到supplementData中
                if (approvalData.usageScenarios) {
                    supplementData = {
                        agentIntroduction: approvalData.usageScenarios.agentIntroduction,
                        usageScenarios: approvalData.usageScenarios.usageScenariosRichText,
                        painPointsSolved: approvalData.usageScenarios.painPointsSolvedRichText,
                        screenshotUrls: approvalData.usageScenarios.screenshots
                    };
                }
            } else {
                // 普通模式：直接使用Agent数据
                agentData = response.data;
            }

            renderAgentBasicInfo(agentData);
            console.log('Agent基础信息加载成功');
        } else {
            throw new Error(response.message || 'API返回错误');
        }

    } catch (error) {
        console.error('加载Agent基础信息失败:', error);
        // 使用模拟数据作为降级方案
        agentData = getMockAgentData();
        renderAgentBasicInfo(agentData);
    }
}

/**
 * 加载补充资料信息
 */
async function loadSupplementInfo() {
    try {
        console.log('开始加载补充资料信息');

        const response = await apiCall(`/api/v1/agent-supplement/${currentAgentId}`, 'GET');

        if (response.code === 200) {
            supplementData = response.data;
            console.log('补充资料信息加载成功');

            // 加载截图数据
            if (supplementData.screenshotUrls) {
                try {
                    const screenshots = typeof supplementData.screenshotUrls === 'string'
                        ? JSON.parse(supplementData.screenshotUrls)
                        : supplementData.screenshotUrls;

                    if (Array.isArray(screenshots)) {
                        uploadedFiles = [...screenshots];
                        console.log('加载已有截图:', uploadedFiles);

                        // 如果是补充资料模式，显示预览
                        if (currentMode === PAGE_MODES.SUPPLEMENT) {
                            setTimeout(() => {
                                loadExistingScreenshots(screenshots);
                            }, 1000);
                        }
                    }
                } catch (e) {
                    console.error('解析截图URL失败:', e);
                }
            }
        } else {
            console.log('补充资料不存在，使用默认数据');
            supplementData = getDefaultSupplementData();
        }

    } catch (error) {
        console.error('加载补充资料失败:', error);
        supplementData = getDefaultSupplementData();
    }
}

/**
 * 加载Tab数据
 */
async function loadTabData(targetTab) {
    if (!targetTab || !currentAgentId) return;
    
    console.log('加载Tab数据:', targetTab);
    
    try {
        switch (targetTab) {
            case '#basic-info':
                // 基础信息已在初始化时加载
                break;
                
            case '#usage-scenarios':
                await loadUsageScenarios();
                break;
                
            case '#screenshots':
                await loadScreenshots();
                break;
                
            case '#preview':
                await loadPreview();
                break;
                
            case '#version-history':
                await loadVersionHistory();
                break;
                
            case '#approval-history':
                await loadApprovalHistory();
                break;
        }
    } catch (error) {
        console.error(`加载Tab数据失败 (${targetTab}):`, error);
    }
}

/**
 * 显示页面加载状态
 */
function showPageLoading(show) {
    const loadingElement = document.querySelector('.page-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

/**
 * 刷新所有数据
 */
async function refreshAllData() {
    console.log('刷新所有数据');
    await loadAllData();
    showToast('数据刷新成功', 'success');
}

/**
 * 返回源页面
 */
function goBackToSourcePage() {
    const source = sessionStorage.getItem('agentDetailSource') || 'agent-approval-list';
    
    if (typeof loadPage === 'function') {
        loadPage(source);
    } else {
        // 降级方案：直接跳转
        window.history.back();
    }
}

/**
 * 渲染Agent基础信息
 */
function renderAgentBasicInfo(data) {
    if (!data) return;

    console.log('渲染Agent基础信息:', data);

    // 基础信息字段映射
    const fieldMappings = {
        'agentName': data.agentName || data.name || '-',
        'agentCode': data.agentCode || data.code || '-',
        'categoryName': data.categoryName || data.category || '-',
        'agentStatus': getStatusBadge(data.status),
        'creatorName': data.creatorName || data.creator || '-',
        'createTime': formatDateTime(data.createdTime || data.createTime),
        'description': data.description || '-'
    };

    // 更新基础信息显示
    Object.keys(fieldMappings).forEach(fieldId => {
        const elements = document.querySelectorAll(`#${fieldId}, #${fieldId}Basic, #${fieldId}Display`);
        elements.forEach(element => {
            if (element) {
                if (fieldId === 'agentStatus') {
                    element.innerHTML = fieldMappings[fieldId];
                } else {
                    element.textContent = fieldMappings[fieldId];
                }
            }
        });
    });

    // 处理版本信息显示
    renderVersionInfo(data);

    // 渲染Agent简介（如果有补充资料）
    if (supplementData && supplementData.agentIntroduction) {
        const introElement = document.querySelector('#agentIntroductionRichText');
        if (introElement) {
            introElement.innerHTML = supplementData.agentIntroduction;
        }

        // 如果是补充资料模式，填充编辑器
        if (currentMode === PAGE_MODES.SUPPLEMENT) {
            // 延迟填充Summernote编辑器，确保编辑器已初始化
            setTimeout(() => {
                if ($('#agentIntroduction').length && $('#agentIntroduction').summernote) {
                    $('#agentIntroduction').summernote('code', supplementData.agentIntroduction || '');
                }
            }, 1000);
        }
    }
}

/**
 * 渲染版本信息
 */
function renderVersionInfo(data) {
    const currentVersionElement = document.getElementById('currentVersion');
    const pendingVersionElement = document.getElementById('pendingVersion');
    const pendingVersionItem = document.getElementById('pendingVersionItem');

    // 显示当前版本
    if (currentVersionElement) {
        currentVersionElement.textContent = data.version || '-';
    }

    // 处理待审批版本信息
    if (currentMode === PAGE_MODES.APPROVAL && window.approvalDetailData) {
        // 审批模式：显示待审批版本
        const approvalData = window.approvalDetailData;
        const pendingVersionNumber = approvalData.basicInfo?.pendingVersionNumber;
        const currentVersionNumber = approvalData.basicInfo?.version;

        if (pendingVersionNumber && pendingVersionNumber !== currentVersionNumber) {
            if (pendingVersionElement) {
                pendingVersionElement.innerHTML = `
                    <span class="badge bg-warning text-dark">${pendingVersionNumber}</span>
                    <small class="text-muted ms-2">审批中</small>
                `;
            }
            if (pendingVersionItem) {
                pendingVersionItem.style.display = 'block';
            }
        } else {
            if (pendingVersionItem) {
                pendingVersionItem.style.display = 'none';
            }
        }
    } else if (currentMode === PAGE_MODES.SUPPLEMENT && selectedVersionNumber) {
        // 补充资料模式：显示要审核的版本
        if (pendingVersionElement) {
            pendingVersionElement.innerHTML = `
                <span class="badge bg-info text-white">${selectedVersionNumber}</span>
                <small class="text-muted ms-2">待审核</small>
            `;
        }
        if (pendingVersionItem) {
            pendingVersionItem.style.display = 'block';
        }
    } else {
        // 其他模式，隐藏待审批版本
        if (pendingVersionItem) {
            pendingVersionItem.style.display = 'none';
        }
    }
}

/**
 * 获取状态徽章HTML
 */
function getStatusBadge(status) {
    const statusMap = {
        1: { class: 'status-active', text: '启用', icon: 'check-circle' },
        0: { class: 'status-inactive', text: '禁用', icon: 'x-circle' },
        2: { class: 'status-pending', text: '审批中', icon: 'clock' }
    };

    const statusInfo = statusMap[status] || { class: 'status-inactive', text: '未知', icon: 'question-circle' };

    return `<span class="status-badge ${statusInfo.class}">
        <i class="bi bi-${statusInfo.icon}"></i>
        ${statusInfo.text}
    </span>`;
}

/**
 * 加载使用场景
 */
async function loadUsageScenarios() {
    try {
        console.log('加载使用场景数据');

        const usageScenariosElement = document.querySelector('#usageScenariosRichText');

        if (supplementData) {
            // 显示使用场景描述
            if (usageScenariosElement) {
                usageScenariosElement.innerHTML = supplementData.usageScenarios ||
                    '<div class="text-muted">暂无使用场景描述</div>';
            }

            // 如果是补充资料模式，填充编辑器
            if (currentMode === PAGE_MODES.SUPPLEMENT) {
                // 延迟填充Summernote编辑器，确保编辑器已初始化
                setTimeout(() => {
                    if ($('#usageScenarios').length && $('#usageScenarios').summernote) {
                        $('#usageScenarios').summernote('code', supplementData.usageScenarios || '');
                    }
                }, 1000);
            }
        }

    } catch (error) {
        console.error('加载使用场景失败:', error);
    }
}

/**
 * 加载截图数据
 */
async function loadScreenshots() {
    try {
        console.log('加载截图数据');

        let screenshots = [];

        if (supplementData && supplementData.screenshotUrls) {
            try {
                screenshots = typeof supplementData.screenshotUrls === 'string'
                    ? JSON.parse(supplementData.screenshotUrls)
                    : supplementData.screenshotUrls;
            } catch (e) {
                console.error('解析截图URL失败:', e);
            }
        }

        renderScreenshotsCarousel(screenshots);

    } catch (error) {
        console.error('加载截图失败:', error);
        renderScreenshotsCarousel([]);
    }
}

/**
 * 渲染截图轮播图
 * @param {Array} screenshots 截图URL数组
 */
function renderScreenshotsCarousel(screenshots) {
    const screenshotsSection = document.getElementById('screenshotsSection');
    const carouselIndicators = document.getElementById('carouselIndicators');
    const carouselInner = document.getElementById('carouselInner');
    const noScreenshotsMessage = document.getElementById('noScreenshotsMessage');

    console.log('渲染轮播图，截图数量:', screenshots ? screenshots.length : 0);

    // 如果没有截图，隐藏轮播图区域，显示无截图消息
    if (!screenshots || screenshots.length === 0) {
        if (screenshotsSection) screenshotsSection.style.display = 'none';
        if (noScreenshotsMessage) noScreenshotsMessage.style.display = 'block';
        return;
    }

    // 显示轮播图区域，隐藏无截图消息
    if (screenshotsSection) screenshotsSection.style.display = 'block';
    if (noScreenshotsMessage) noScreenshotsMessage.style.display = 'none';

    // 清空现有内容
    if (carouselIndicators) carouselIndicators.innerHTML = '';
    if (carouselInner) carouselInner.innerHTML = '';

    // 生成轮播图指示器和内容
    screenshots.forEach((screenshot, index) => {
        // 创建指示器
        if (carouselIndicators) {
            const indicator = document.createElement('button');
            indicator.type = 'button';
            indicator.setAttribute('data-bs-target', '#screenshotsCarousel');
            indicator.setAttribute('data-bs-slide-to', index.toString());
            if (index === 0) {
                indicator.classList.add('active');
                indicator.setAttribute('aria-current', 'true');
            }
            indicator.setAttribute('aria-label', `截图 ${index + 1}`);
            carouselIndicators.appendChild(indicator);
        }

        // 创建轮播项
        if (carouselInner) {
            const carouselItem = document.createElement('div');
            carouselItem.classList.add('carousel-item');
            if (index === 0) {
                carouselItem.classList.add('active');
            }

            const img = document.createElement('img');
            img.src = screenshot;
            img.classList.add('d-block', 'w-100');
            img.alt = `Chrome插件效果截图 ${index + 1}`;
            img.style.cursor = 'pointer';

            // 添加错误处理
            img.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
            };

            // 添加点击放大功能
            img.addEventListener('click', () => {
                showImageModal(screenshot, `Chrome插件效果截图 ${index + 1}`);
            });

            carouselItem.appendChild(img);
            carouselInner.appendChild(carouselItem);
        }
    });

    console.log('轮播图渲染完成，共', screenshots.length, '张截图');
}

/**
 * 显示图片放大模态框
 * @param {string} imageSrc 图片URL
 * @param {string} title 图片标题
 */
function showImageModal(imageSrc, title) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="imageModalLabel">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageSrc}" class="img-fluid" alt="${title}" style="max-height: 70vh;">
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('imageModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('imageModal'));

    // 绑定模态框关闭事件
    document.getElementById('imageModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('imageModal').remove();
    });

    modal.show();
}

/**
 * 加载预览数据
 */
async function loadPreview() {
    try {
        console.log('加载预览数据');

        const previewContainer = document.querySelector('#previewContainer');
        if (!previewContainer) return;

        // 生成预览内容
        const previewHtml = generatePreviewContent();
        previewContainer.innerHTML = previewHtml;

    } catch (error) {
        console.error('加载预览失败:', error);
    }
}

/**
 * 生成预览内容
 */
function generatePreviewContent() {
    if (!agentData) return '<div class="text-muted">暂无预览数据</div>';

    return `
        <div class="preview-section">
            <h6><i class="bi bi-info-circle"></i> Agent基本信息</h6>
            <div class="preview-content">
                <p><strong>名称：</strong>${agentData.agentName || agentData.name || '-'}</p>
                <p><strong>编码：</strong>${agentData.agentCode || agentData.code || '-'}</p>
                <p><strong>分类：</strong>${agentData.categoryName || agentData.category || '-'}</p>
                <p><strong>描述：</strong>${agentData.description || '-'}</p>
            </div>
        </div>

        ${supplementData && supplementData.agentIntroduction ? `
        <div class="preview-section">
            <h6><i class="bi bi-file-text"></i> Agent简介</h6>
            <div class="preview-content rich-text-content">
                ${supplementData.agentIntroduction}
            </div>
        </div>
        ` : ''}

        ${supplementData && supplementData.usageScenarios ? `
        <div class="preview-section">
            <h6><i class="bi bi-lightbulb"></i> 使用场景</h6>
            <div class="preview-content rich-text-content">
                ${supplementData.usageScenarios}
            </div>
        </div>
        ` : ''}
    `;
}

/**
 * 加载版本历史
 */
async function loadVersionHistory() {
    try {
        console.log('加载版本历史');

        let response;
        if (currentMode === PAGE_MODES.APPROVAL) {
            // 审批模式：使用审批接口
            response = await apiCall(`/api/v1/agent-approval/versions/${currentAgentId}`, 'GET');
        } else {
            // 普通模式：使用审批接口（暂时统一使用）
            response = await apiCall(`/api/v1/agent-approval/versions/${currentAgentId}`, 'GET');
        }

        if (response.code === 200) {
            renderVersionHistory(response.data);
        } else {
            console.error('版本历史加载失败:', response.message);
            renderVersionHistory([]);
        }

    } catch (error) {
        console.error('加载版本历史失败:', error);
        renderVersionHistory([]);
    }
}

/**
 * 渲染版本历史（扁平化设计）
 */
function renderVersionHistory(versions) {
    const versionContainer = document.querySelector('#versionHistoryContent');
    if (!versionContainer) return;

    if (!versions || versions.length === 0) {
        versionContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-clock-history" style="font-size: 3rem; color: #dee2e6;"></i>
                <h5 class="text-muted mt-2">暂无版本历史</h5>
                <p class="text-muted">还没有版本记录</p>
            </div>
        `;
        return;
    }

    const versionsHtml = versions.map((version, index) => {
        // 构建悬停提示内容
        const tooltipContent = [
            `版本: v${version.versionNumber}`,
            `状态: ${getVersionStatusText(version.approvalStatus)}`,
            `创建时间: ${formatDateTime(version.createdTime)}`,
            version.creatorName ? `创建者: ${version.creatorName}` : '',
            version.upgradeContent ? `更新内容: ${version.upgradeContent.substring(0, 100)}${version.upgradeContent.length > 100 ? '...' : ''}` : '',
            version.description ? `说明: ${version.description.substring(0, 100)}${version.description.length > 100 ? '...' : ''}` : ''
        ].filter(item => item).join('\n');

        return `
            <div class="timeline-item ${index === 0 ? 'timeline-item-current' : ''}">
                <div class="timeline-marker"></div>
                <div class="timeline-content timeline-tooltip" data-tooltip="${tooltipContent.replace(/"/g, '&quot;')}">
                    <div class="version-compact">
                        <div class="version-number">v${version.versionNumber}</div>
                        <div class="version-status ${getVersionStatusClass(version.approvalStatus)}">
                            <i class="bi bi-${getVersionStatusIcon(version.approvalStatus)}"></i>
                            ${getVersionStatusText(version.approvalStatus)}
                        </div>
                        <div class="version-meta">
                            ${formatDateTime(version.createdTime)}
                            ${version.creatorName ? `<span class="version-creator">by ${version.creatorName}</span>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    versionContainer.innerHTML = versionsHtml;
}

/**
 * 加载审批历史
 */
async function loadApprovalHistory() {
    try {
        console.log('加载审批历史');

        const response = await apiCall(`/api/v1/agent-approval/history/${currentAgentId}`, 'GET');

        if (response.code === 200) {
            renderApprovalHistory(response.data);
        } else {
            renderApprovalHistory([]);
        }

    } catch (error) {
        console.error('加载审批历史失败:', error);
        renderApprovalHistory([]);
    }
}

/**
 * 渲染审批历史（扁平化设计）
 */
function renderApprovalHistory(history) {
    const historyContainer = document.querySelector('#approvalHistoryContent');
    if (!historyContainer) return;

    if (!history || history.length === 0) {
        historyContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-list-check" style="font-size: 3rem; color: #dee2e6;"></i>
                <h5 class="text-muted mt-2">暂无审批历史</h5>
                <p class="text-muted">还没有审批记录</p>
            </div>
        `;
        return;
    }

    const historyHtml = history.map((record, index) => {
        // 构建悬停提示内容
        const tooltipContent = [
            `状态: ${getApprovalStatusText(record.approvalStatus)}`,
            `版本: v${record.versionNumber || '1.0.0'}`,
            `审批人: ${record.approverName || '系统'}`,
            `审批时间: ${formatDateTime(record.createdTime || record.approvalTime)}`,
            record.submissionTime ? `提交时间: ${formatDateTime(record.submissionTime)}` : '',
            record.approvalComment || record.approvalOpinion ? `审批意见: ${(record.approvalComment || record.approvalOpinion).substring(0, 100)}${(record.approvalComment || record.approvalOpinion).length > 100 ? '...' : ''}` : ''
        ].filter(item => item).join('\n');

        return `
            <div class="timeline-item ${index === 0 ? 'timeline-item-current' : ''}">
                <div class="timeline-marker"></div>
                <div class="timeline-content timeline-tooltip" data-tooltip="${tooltipContent.replace(/"/g, '&quot;')}">
                    <div class="approval-compact">
                        <div class="approval-status ${getApprovalStatusClass(record.approvalStatus)}">
                            <i class="bi bi-${getApprovalStatusIcon(record.approvalStatus)}"></i>
                            ${getApprovalStatusText(record.approvalStatus)}
                        </div>
                        <div class="approval-version">v${record.versionNumber || '1.0.0'}</div>
                        <div class="approval-user">${record.approverName || '系统'}</div>
                        <div class="approval-date">${formatDateTime(record.createdTime || record.approvalTime)}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    historyContainer.innerHTML = historyHtml;
}

/**
 * 处理审批通过
 */
async function handleApprove() {
    const comment = await showApprovalDialog('审批通过', '请填写审批意见（可选）：');
    if (comment === null) return; // 用户取消

    try {
        // 构建审批请求数据
        const requestData = {
            agentId: currentAgentId,
            approvalResult: 2, // 2-审批通过
            approvalComment: comment
        };

        // 获取待审批版本ID（如果有）
        const pendingVersionId = window.approvalDetailData?.basicInfo?.pendingVersionId;
        if (pendingVersionId) {
            requestData.agentVersionId = pendingVersionId;
            console.log('审批通过，包含版本ID:', pendingVersionId);
        }

        const response = await apiCall('/api/v1/agent-approval/approve', 'POST', requestData);

        if (response.code === 200) {
            const versionInfo = pendingVersionId ?
                `（版本：${window.approvalDetailData?.basicInfo?.pendingVersionNumber || ''}）` : '';
            showToast('审批成功' + versionInfo, 'success');
            setTimeout(() => goBackToSourcePage(), 1500);
        } else {
            showToast('审批失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('审批失败:', error);
        showToast('审批失败', 'error');
    }
}

/**
 * 处理审批不通过
 */
async function handleReject() {
    const comment = await showApprovalDialog('审批不通过', '请填写不通过原因：', true);
    if (comment === null) return; // 用户取消

    if (!comment.trim()) {
        showToast('请填写不通过原因', 'warning');
        return;
    }

    try {
        // 构建审批请求数据
        const requestData = {
            agentId: currentAgentId,
            approvalResult: 3, // 3-审批不通过
            approvalComment: comment
        };

        // 获取待审批版本ID（如果有）
        const pendingVersionId = window.approvalDetailData?.basicInfo?.pendingVersionId;
        if (pendingVersionId) {
            requestData.agentVersionId = pendingVersionId;
            console.log('审批不通过，包含版本ID:', pendingVersionId);
        }

        const response = await apiCall('/api/v1/agent-approval/approve', 'POST', requestData);

        if (response.code === 200) {
            const versionInfo = pendingVersionId ?
                `（版本：${window.approvalDetailData?.basicInfo?.pendingVersionNumber || ''}）` : '';
            showToast('审批成功' + versionInfo, 'success');
            setTimeout(() => goBackToSourcePage(), 1500);
        } else {
            showToast('审批失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('审批失败:', error);
        showToast('审批失败', 'error');
    }
}

/**
 * 处理保存草稿
 */
async function handleSaveDraft() {
    try {
        const formData = collectFormData();
        formData.status = 0; // 草稿状态

        const response = await apiCall('/api/v1/agent-supplement/save', 'POST', formData);

        if (response.code === 200) {
            showToast('保存成功', 'success');
            // 重新加载补充资料数据
            await loadSupplementInfo();
        } else {
            showToast('保存失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('保存失败:', error);
        showToast('保存失败', 'error');
    }
}

/**
 * 处理提交审核
 */
async function handleSubmitForApproval() {
    try {
        const formData = collectFormData();
        formData.status = 1; // 已提交状态

        // 添加版本信息（如果是从版本历史申请发布）
        if (selectedVersionId) {
            formData.versionId = selectedVersionId;
            formData.versionNumber = selectedVersionNumber;
        }

        const response = await apiCall('/api/v1/agent-supplement/submit', 'POST', formData);

        if (response.code === 200) {
            showToast('提交成功', 'success');
            // 清除版本信息
            sessionStorage.removeItem('selectedVersionId');
            sessionStorage.removeItem('selectedVersionNumber');
            setTimeout(() => goBackToSourcePage(), 1500);
        } else {
            showToast('提交失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('提交失败:', error);
        showToast('提交失败', 'error');
    }
}

/**
 * 处理预览
 */
function handlePreview() {
    // 收集当前表单数据
    const formData = collectFormData();

    // 显示预览对话框
    showPreviewDialog(formData);
}

/**
 * 收集表单数据
 */
function collectFormData() {
    // 使用Summernote的API获取内容
    const agentIntroduction = $('#agentIntroduction').length ? $('#agentIntroduction').summernote('code') : '';
    const usageScenarios = $('#usageScenarios').length ? $('#usageScenarios').summernote('code') : '';

    return {
        agentId: currentAgentId,
        agentIntroduction,
        usageScenarios,
        screenshotUrls: uploadedFiles // 使用全局的uploadedFiles数组
    };
}

/**
 * 工具函数
 */

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '-';

    try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTime;
    }
}

// 获取版本状态样式类
function getVersionStatusClass(status) {
    const statusMap = {
        0: 'status-draft',
        1: 'status-pending',
        2: 'status-approved',
        3: 'status-rejected'
    };
    return statusMap[status] || 'status-draft';
}

// 获取版本状态文本
function getVersionStatusText(status) {
    const statusMap = {
        0: '草稿',
        1: '审批中',
        2: '已发布',
        3: '已拒绝'
    };
    return statusMap[status] || '未知';
}

// 获取版本状态图标
function getVersionStatusIcon(status) {
    const iconMap = {
        0: 'file-earmark',
        1: 'clock',
        2: 'check-circle',
        3: 'x-circle'
    };
    return iconMap[status] || 'question-circle';
}

// 获取审批状态样式类
function getApprovalStatusClass(status) {
    const statusMap = {
        1: 'status-pending',    // 审批中
        2: 'status-approved',   // 审批通过
        3: 'status-rejected'    // 审批不通过
    };
    return statusMap[status] || 'status-pending';
}

// 获取审批状态文本
function getApprovalStatusText(status) {
    const statusMap = {
        1: '审批中',      // 审批中
        2: '审批通过',    // 审批通过
        3: '审批不通过'   // 审批不通过
    };
    return statusMap[status] || '未知状态';
}

// 获取审批状态图标
function getApprovalStatusIcon(status) {
    const iconMap = {
        1: 'clock',         // 审批中
        2: 'check-circle',  // 审批通过
        3: 'x-circle'       // 审批不通过
    };
    return iconMap[status] || 'question-circle';
}

// 获取默认补充资料数据
function getDefaultSupplementData() {
    return {
        agentId: currentAgentId,
        agentIntroduction: '',
        usageScenarios: '',
        painPointsSolved: '',
        screenshotUrls: [],
        status: 0
    };
}

// 获取模拟Agent数据
function getMockAgentData() {
    return {
        id: currentAgentId,
        agentName: 'Demo Agent',
        agentCode: 'DEMO_001',
        categoryName: '演示分类',
        status: 1,
        creatorName: '演示用户',
        createdTime: new Date().toISOString(),
        description: '这是一个演示Agent，用于展示页面功能。'
    };
}

// 获取认证Token
function getToken() {
    // 尝试从全局变量获取
    if (typeof authToken !== 'undefined' && authToken) {
        return authToken;
    }

    // 尝试从localStorage获取
    return localStorage.getItem('token') ||
           localStorage.getItem('authToken') ||
           sessionStorage.getItem('token') ||
           sessionStorage.getItem('authToken');
}

/**
 * 初始化富文本编辑器
 */
function initRichTextEditors() {
    if (typeof $ === 'undefined' || typeof $.fn.summernote === 'undefined') {
        console.warn('Summernote未加载，跳过富文本编辑器初始化');
        return;
    }

    const editorConfig = {
        height: 200,
        lang: 'zh-CN',
        placeholder: '请输入内容...',
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        // 修复弹框抖动问题
        dialogsInBody: true,
        dialogsFade: true,
        // 设置模态框的z-index
        popover: {
            image: [
                ['image', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],
                ['float', ['floatLeft', 'floatRight', 'floatNone']],
                ['remove', ['removeMedia']]
            ],
            link: [
                ['link', ['linkDialogShow', 'unlink']]
            ],
            table: [
                ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],
                ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]
            ]
        },
        // 自定义回调函数
        callbacks: {
            onImageUpload: function(files) {
                // 处理图片上传
                console.log('富文本编辑器图片上传:', files);
                handleSummernoteImageUpload(files, this);
            },
            onDialogShown: function() {
                // 当对话框显示时，调整z-index
                $('.note-modal').css('z-index', 1060);
            }
        }
    };

    // 初始化Agent简介编辑器
    const introEditor = $('#agentIntroduction');
    if (introEditor.length) {
        introEditor.summernote(editorConfig);
    }

    // 初始化使用场景编辑器
    const scenariosEditor = $('#usageScenarios');
    if (scenariosEditor.length) {
        scenariosEditor.summernote(editorConfig);
    }

    // 修复Summernote模态框z-index问题
    $(document).on('show.bs.modal', '.note-modal', function() {
        $(this).css('z-index', 1060);
    });

    // 修复Bootstrap模态框与Summernote的冲突
    $(document).on('hidden.bs.modal', '.note-modal', function() {
        $('.modal-backdrop').remove();
    });
}

/**
 * 显示审批对话框
 */
function showApprovalDialog(title, message, required = false) {
    return new Promise((resolve) => {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="approvalModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="approvalComment" class="form-label">${message}</label>
                                <textarea class="form-control" id="approvalComment" rows="4"
                                          placeholder="请输入审批意见..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmApproval">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('approvalModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
        const commentInput = document.getElementById('approvalComment');
        const confirmBtn = document.getElementById('confirmApproval');

        // 绑定确认按钮事件
        confirmBtn.addEventListener('click', () => {
            const comment = commentInput.value.trim();

            if (required && !comment) {
                showToast('请填写审批意见', 'warning');
                return;
            }

            modal.hide();
            resolve(comment);
        });

        // 绑定模态框关闭事件
        document.getElementById('approvalModal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('approvalModal').remove();
            resolve(null);
        });

        modal.show();

        // 聚焦到输入框
        setTimeout(() => {
            commentInput.focus();
        }, 500);
    });
}

/**
 * 显示预览对话框
 */
function showPreviewDialog(formData) {
    const previewHtml = generatePreviewDialogContent(formData);

    const modalHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-eye"></i> 预览效果
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        ${previewHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('previewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('previewModal'));

    // 绑定模态框关闭事件
    document.getElementById('previewModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('previewModal').remove();
    });

    modal.show();
}

/**
 * 生成预览对话框内容
 */
function generatePreviewDialogContent(formData) {
    return `
        <div class="preview-content">
            <div class="preview-section">
                <h6><i class="bi bi-info-circle"></i> Agent基本信息</h6>
                <div class="preview-info">
                    <p><strong>名称：</strong>${agentData?.agentName || agentData?.name || '-'}</p>
                    <p><strong>编码：</strong>${agentData?.agentCode || agentData?.code || '-'}</p>
                    <p><strong>分类：</strong>${agentData?.categoryName || agentData?.category || '-'}</p>
                    <p><strong>描述：</strong>${agentData?.description || '-'}</p>
                </div>
            </div>

            ${formData.agentIntroduction ? `
            <div class="preview-section">
                <h6><i class="bi bi-file-text"></i> Agent简介</h6>
                <div class="preview-content-text rich-text-content">
                    ${formData.agentIntroduction}
                </div>
            </div>
            ` : ''}

            ${formData.usageScenarios ? `
            <div class="preview-section">
                <h6><i class="bi bi-lightbulb"></i> 使用场景</h6>
                <div class="preview-content-text rich-text-content">
                    ${formData.usageScenarios}
                </div>
            </div>
            ` : ''}

            ${formData.screenshotUrls && formData.screenshotUrls.length > 0 ? `
            <div class="preview-section">
                <h6><i class="bi bi-images"></i> Chrome插件截图</h6>
                <div class="preview-screenshots">
                    ${formData.screenshotUrls.map(url => `
                        <img src="${url}" alt="截图" class="preview-screenshot" />
                    `).join('')}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

/**
 * 预览图片
 */
function previewImage(imageUrl) {
    const modalHtml = `
        <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-image"></i> 图片预览
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageUrl}" alt="预览图片" class="img-fluid" style="max-height: 70vh;" />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <a href="${imageUrl}" target="_blank" class="btn btn-primary">
                            <i class="bi bi-download"></i> 下载
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('imagePreviewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));

    // 绑定模态框关闭事件
    document.getElementById('imagePreviewModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('imagePreviewModal').remove();
    });

    modal.show();
}

/**
 * 更新模式特定的内容显示
 */
function updateModeSpecificContent() {
    const viewModeElements = document.querySelectorAll('.view-mode-content');
    const supplementModeElements = document.querySelectorAll('.supplement-mode-content');

    if (currentMode === PAGE_MODES.SUPPLEMENT) {
        // 补充资料模式：显示编辑器，隐藏只读内容
        viewModeElements.forEach(el => el.style.display = 'none');
        supplementModeElements.forEach(el => el.style.display = 'block');
    } else {
        // 查看/审批模式：显示只读内容，隐藏编辑器
        viewModeElements.forEach(el => el.style.display = 'block');
        supplementModeElements.forEach(el => el.style.display = 'none');
    }
}

/**
 * 处理Summernote富文本编辑器的图片上传
 * @param {FileList} files 上传的文件列表
 * @param {Object} editor Summernote编辑器实例
 */
async function handleSummernoteImageUpload(files, editor) {
    try {
        for (const file of files) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showToast('只能上传图片文件', 'error');
                continue;
            }

            // 验证文件大小（5MB限制）
            if (file.size > 5 * 1024 * 1024) {
                showToast(`图片 ${file.name} 超过5MB限制`, 'error');
                continue;
            }

            // 显示上传进度
            showToast('正在上传图片...', 'info');

            try {
                // 上传文件到服务器
                const formData = new FormData();
                formData.append('file', file);
                formData.append('businessType', 'agent-richtext-image');

                const response = await uploadFile('/api/v1/files/upload', formData);

                if (response.code === 200 && response.data) {
                    // 获取公开预览URL
                    const fileId = response.data.fileId;
                    const publicPreviewUrl = `/api/v1/files/public/${fileId}/preview`;

                    // 将图片插入到编辑器中
                    $(editor).summernote('insertImage', publicPreviewUrl, function($image) {
                        // 设置图片属性
                        $image.attr('alt', file.name);
                        $image.attr('title', file.name);
                        $image.addClass('img-fluid'); // 添加响应式类

                        // 设置最大宽度
                        $image.css({
                            'max-width': '100%',
                            'height': 'auto'
                        });
                    });

                    showToast('图片上传成功', 'success');
                    console.log('富文本编辑器图片上传成功:', file.name, '预览URL:', publicPreviewUrl);
                } else {
                    throw new Error(response.message || '上传失败');
                }
            } catch (error) {
                console.error('富文本编辑器图片上传失败:', error);
                showToast(`图片 ${file.name} 上传失败: ${error.message}`, 'error');
            }
        }
    } catch (error) {
        console.error('处理富文本编辑器图片上传时出错:', error);
        showToast('图片上传处理失败', 'error');
    }
}

/**
 * 初始化文件上传功能
 */
function initFileUpload() {
    const uploadArea = document.getElementById('screenshotUpload');
    const fileInput = document.getElementById('screenshotInput');

    if (!uploadArea || !fileInput) {
        console.warn('文件上传元素未找到');
        return;
    }

    console.log('初始化文件上传功能');

    // 点击上传
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', async function(e) {
        await handleFiles(e.target.files);
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', async function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        await handleFiles(e.dataTransfer.files);
    });
}

/**
 * 处理上传的文件
 */
async function handleFiles(files) {
    for (const file of files) {
        if (file.type.startsWith('image/')) {
            if (file.size > 5 * 1024 * 1024) {
                showToast(`文件 ${file.name} 超过5MB限制`, 'error');
                continue;
            }

            // 创建本地预览
            const reader = new FileReader();
            reader.onload = async function(e) {
                // 先显示本地预览
                addScreenshotPreview(e.target.result, file.name);

                try {
                    // 上传到服务器并获取公开URL
                    const publicUrl = await uploadScreenshotFile(file);

                    // 更新预览图片的src为服务器URL
                    if (publicUrl) {
                        const previewImages = document.querySelectorAll('#screenshotPreview .screenshot-item img');
                        const lastImage = previewImages[previewImages.length - 1];
                        if (lastImage) {
                            lastImage.src = publicUrl;
                            console.log('预览图片已更新为服务器URL:', publicUrl);
                        }
                    }
                } catch (error) {
                    console.error('上传失败，保持本地预览:', error);
                    showToast('上传失败: ' + error.message, 'error');
                }
            };
            reader.readAsDataURL(file);
        } else {
            showToast(`文件 ${file.name} 不是有效的图片格式`, 'error');
        }
    }
}

/**
 * 上传文件到服务器
 */
async function uploadScreenshotFile(file) {
    try {
        console.log('开始上传文件:', file.name);

        // 检查认证状态
        const token = getToken();
        if (!token) {
            throw new Error('未找到认证Token，请重新登录');
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('businessType', 'agent-screenshot');

        // 使用admin-common.js中的uploadFile函数
        const response = await uploadFile('/api/v1/files/upload', formData);

        if (response.code === 200 && response.data) {
            // 使用公开预览URL
            const fileId = response.data.fileId;
            const publicPreviewUrl = `/api/v1/files/public/${fileId}/preview`;
            uploadedFiles.push(publicPreviewUrl);
            console.log('文件上传成功:', file.name, '文件ID:', fileId, '公开预览URL:', publicPreviewUrl);
            return publicPreviewUrl;
        } else {
            throw new Error(response.message || '上传失败');
        }
    } catch (error) {
        console.error('文件上传失败:', error);
        throw error;
    }
}

/**
 * 添加截图预览
 */
function addScreenshotPreview(src, fileName) {
    const previewContainer = document.getElementById('screenshotPreview');
    if (!previewContainer) {
        console.warn('截图预览容器未找到');
        return;
    }

    const previewItem = document.createElement('div');
    previewItem.className = 'screenshot-item';
    previewItem.innerHTML = `
        <img src="${src}" alt="${fileName}" title="${fileName}" onclick="previewImage('${src}')">
        <button type="button" class="screenshot-remove" onclick="removeScreenshot(this)">
            <i class="bi bi-x"></i>
        </button>
    `;

    previewContainer.appendChild(previewItem);
}

/**
 * 移除截图
 */
function removeScreenshot(button) {
    const item = button.closest('.screenshot-item');
    const img = item.querySelector('img');
    const src = img.src;

    console.log('删除图片，原始src:', src);

    // 将绝对URL转换为相对URL进行匹配
    let relativeUrl = src;
    if (src.startsWith('http')) {
        try {
            const url = new URL(src);
            relativeUrl = url.pathname + url.search;
        } catch (e) {
            console.warn('URL解析失败:', e);
        }
    }

    // 从上传文件列表中移除
    let index = uploadedFiles.indexOf(src);
    if (index === -1) {
        index = uploadedFiles.indexOf(relativeUrl);
    }

    if (index > -1) {
        uploadedFiles.splice(index, 1);
        console.log('成功从列表中删除，剩余文件:', uploadedFiles);
    } else {
        console.warn('未在uploadedFiles中找到要删除的URL:', src, relativeUrl);
    }

    // 移除DOM元素
    item.remove();
}

/**
 * 加载已有截图到预览区域
 */
function loadExistingScreenshots(screenshots) {
    const previewContainer = document.getElementById('screenshotPreview');
    if (!previewContainer) {
        console.warn('截图预览容器未找到');
        return;
    }

    // 清空现有预览
    previewContainer.innerHTML = '';

    // 添加已有截图
    screenshots.forEach(url => {
        addScreenshotPreview(url, '截图');
    });

    console.log('已加载', screenshots.length, '张已有截图');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (typeof initUnifiedDetailPage === 'function') {
        initUnifiedDetailPage();
    }
});
