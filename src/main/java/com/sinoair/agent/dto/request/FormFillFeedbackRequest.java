package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 表单回填反馈请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "表单回填反馈请求")
public class FormFillFeedbackRequest {

    @Schema(description = "回填记录ID", example = "789")
    private Long recordId;

    @Schema(description = "用户反馈：1-点赞，0-踩", example = "1")
    private Integer userFeedback;

    @Schema(description = "反馈备注", example = "回填效果很好，节省了很多时间！")
    private String feedbackComment;
}
