package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 表单回填记录请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "表单回填记录请求")
public class FormFillRecordRequest {

    @Schema(description = "识别记录ID", example = "123")
    private Long recognitionRecordId;

    @Schema(description = "页面绑定配置ID", example = "456")
    private Long pageBindingId;

    @Schema(description = "目标页面URL", example = "https://example.com/form")
    private String targetUrl;

    @Schema(description = "回填的数据内容（JSON格式）", example = "{\"name\":\"张三\",\"age\":30}")
    private String fillData;

    @Schema(description = "回填结果：1-成功，2-部分成功，3-失败", example = "1")
    private Integer fillResult;

    @Schema(description = "错误信息（回填失败时记录）")
    private String errorMessage;

    @Schema(description = "反馈备注")
    private String feedbackComment;
}
