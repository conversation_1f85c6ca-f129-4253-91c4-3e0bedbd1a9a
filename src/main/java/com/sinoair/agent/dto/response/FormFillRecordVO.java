package com.sinoair.agent.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 表单回填记录响应VO
 * 
 * <AUTHOR> Team
 */
@Data
@Schema(description = "表单回填记录响应")
public class FormFillRecordVO {

    @Schema(description = "回填记录ID")
    private Long id;

    @Schema(description = "识别记录ID")
    private Long recognitionRecordId;

    @Schema(description = "页面绑定配置ID")
    private Long pageBindingId;

    @Schema(description = "页面绑定名称")
    private String bindingName;

    @Schema(description = "目标页面URL")
    private String targetUrl;

    @Schema(description = "回填的数据内容（JSON格式）")
    private String fillData;

    @Schema(description = "回填结果：1-成功，2-部分成功，3-失败")
    private Integer fillResult;

    @Schema(description = "回填结果描述")
    private String fillResultDesc;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "执行回填的用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户真实姓名")
    private String userRealName;

    @Schema(description = "用户反馈：1-点赞，0-踩，null-无反馈")
    private Integer userFeedback;

    @Schema(description = "用户反馈描述")
    private String userFeedbackDesc;

    @Schema(description = "反馈时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime feedbackTime;

    @Schema(description = "反馈备注")
    private String feedbackComment;

    @Schema(description = "回填时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fillTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}
