package com.sinoair.agent.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 表单回填统计响应VO
 * 
 * <AUTHOR> Team
 */
@Data
@Schema(description = "表单回填统计响应")
public class FormFillStatisticsVO {

    @Schema(description = "总回填记录数")
    private Long totalRecords;

    @Schema(description = "成功回填记录数")
    private Long successRecords;

    @Schema(description = "成功率（百分比）")
    private Double successRate;

    @Schema(description = "各回填结果统计")
    private List<Map<String, Object>> fillResultStats;

    @Schema(description = "各用户反馈统计")
    private List<Map<String, Object>> userFeedbackStats;

    @Schema(description = "各用户回填统计")
    private List<Map<String, Object>> userFillStats;

    @Schema(description = "各页面绑定回填统计")
    private List<Map<String, Object>> pageBindingFillStats;

    @Schema(description = "热门回填页面")
    private List<Map<String, Object>> popularFillPages;

    @Schema(description = "用户回填活跃度")
    private List<Map<String, Object>> userFillActivity;
}
