package com.sinoair.agent.config;

import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * MinIO配置类
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.minio", name = "enabled", havingValue = "true", matchIfMissing = true)
public class MinioConfig {

    private final MinioProperties minioProperties;

    /**
     * 创建MinIO客户端
     */
    @Bean
    public MinioClient minioClient() {
        try {
            // 创建自定义的OkHttpClient，设置更长的超时时间和重试机制
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(Math.max(minioProperties.getConnectTimeout(), 30000), TimeUnit.MILLISECONDS)
                    .writeTimeout(Math.max(minioProperties.getWriteTimeout(), 120000), TimeUnit.MILLISECONDS)
                    .readTimeout(Math.max(minioProperties.getReadTimeout(), 30000), TimeUnit.MILLISECONDS)
                    .retryOnConnectionFailure(true)
                    .build();

            System.out.println("MinIO客户端初始化信息: AccessKey=" + minioProperties.getAccessKey() + ", SecretKey=" + minioProperties.getSecretKey());
            System.out.println(minioProperties.getBucketName());

            // 创建MinIO客户端
            MinioClient client = MinioClient.builder()
                    .endpoint(minioProperties.getEndpoint())
                    .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                    .region(minioProperties.getRegion())
                    .httpClient(httpClient)
                    .build();

            log.info("MinIO客户端初始化成功: endpoint={}, bucket={}",
                    minioProperties.getEndpoint(), minioProperties.getBucketName());

            // 测试连接
            // testConnection(client);
            
            return client;
        } catch (Exception e) {
            log.error("MinIO客户端初始化失败", e);
            throw new RuntimeException("MinIO客户端初始化失败", e);
        }
    }

    /**
     * 测试MinIO连接
     */
    private void testConnection(MinioClient client) {
        try {
            log.info("测试MinIO连接...");
            var buckets = client.listBuckets();
            log.info("MinIO连接测试成功，发现{}个存储桶", buckets.size());
        } catch (Exception e) {
            log.warn("MinIO连接测试失败，但客户端仍会创建: {}", e.getMessage());
            // 不抛出异常，允许应用启动，但记录警告
        }
    }
}
