package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.dto.request.FormFillFeedbackRequest;
import com.sinoair.agent.dto.request.FormFillRecordRequest;
import com.sinoair.agent.dto.response.FormFillRecordVO;
import com.sinoair.agent.dto.response.FormFillStatisticsVO;
import com.sinoair.agent.entity.FormFillRecord;
import com.sinoair.agent.mapper.FormFillRecordMapper;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.FormFillRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单回填记录服务实现类
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FormFillRecordServiceImpl implements FormFillRecordService {

    private final FormFillRecordMapper formFillRecordMapper;

    @Override
    @Transactional
    public Result<FormFillRecordVO> createFillRecord(FormFillRecordRequest request) {
        try {
            // 参数验证
            if (request == null) {
                return Result.error(ResultCode.BAD_REQUEST, "请求参数不能为空");
            }
            if (request.getPageBindingId() == null) {
                return Result.error(ResultCode.BAD_REQUEST, "页面绑定配置ID不能为空");
            }
            if (request.getTargetUrl() == null || request.getTargetUrl().trim().isEmpty()) {
                return Result.error(ResultCode.BAD_REQUEST, "目标页面URL不能为空");
            }
            if (request.getFillData() == null || request.getFillData().trim().isEmpty()) {
                return Result.error(ResultCode.BAD_REQUEST, "回填数据不能为空");
            }
            if (request.getFillResult() == null) {
                return Result.error(ResultCode.BAD_REQUEST, "回填结果不能为空");
            }

            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();
            if (currentUser == null) {
                return Result.error(ResultCode.UNAUTHORIZED, "用户未登录");
            }

            // 创建回填记录
            FormFillRecord record = new FormFillRecord();
            BeanUtils.copyProperties(request, record);

            // 如果recognitionRecordId为-1，表示使用JSON数据，设置为NULL
            if (request.getRecognitionRecordId() != null && request.getRecognitionRecordId() == -1) {
                record.setRecognitionRecordId(null);
            }

            record.setUserId(currentUser.getId());
            record.setFillTime(LocalDateTime.now());
            record.setCreatedTime(LocalDateTime.now());
            record.setUpdatedTime(LocalDateTime.now());

            // 保存记录
            int result = formFillRecordMapper.insert(record);
            if (result <= 0) {
                return Result.error("创建回填记录失败");
            }

            // 查询详细信息并返回
            FormFillRecord savedRecord = formFillRecordMapper.selectByIdWithDetails(record.getId());
            FormFillRecordVO vo = convertToVO(savedRecord);

            log.info("创建回填记录成功: recordId={}, userId={}, recognitionRecordId={}", 
                    record.getId(), currentUser.getId(), request.getRecognitionRecordId());

            return Result.success(vo);

        } catch (Exception e) {
            log.error("创建回填记录失败", e);
            return Result.error("创建回填记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<FormFillRecordVO> updateUserFeedback(FormFillFeedbackRequest request) {
        try {
            // 参数验证
            if (request == null) {
                return Result.error(ResultCode.BAD_REQUEST, "请求参数不能为空");
            }
            if (request.getRecordId() == null) {
                return Result.error(ResultCode.BAD_REQUEST, "回填记录ID不能为空");
            }
            if (request.getUserFeedback() == null) {
                return Result.error(ResultCode.BAD_REQUEST, "用户反馈不能为空");
            }
            if (request.getUserFeedback() != 0 && request.getUserFeedback() != 1) {
                return Result.error(ResultCode.BAD_REQUEST, "用户反馈值无效，只能是0或1");
            }

            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();
            if (currentUser == null) {
                return Result.error(ResultCode.UNAUTHORIZED, "用户未登录");
            }

            // 检查记录是否存在且属于当前用户
            FormFillRecord existingRecord = formFillRecordMapper.selectById(request.getRecordId());
            if (existingRecord == null) {
                return Result.error(ResultCode.NOT_FOUND, "回填记录不存在");
            }

            if (!existingRecord.getUserId().equals(currentUser.getId())) {
                return Result.error(ResultCode.FORBIDDEN, "无权限操作此记录");
            }

            // 更新用户反馈
            int result = formFillRecordMapper.updateUserFeedback(
                    request.getRecordId(),
                    request.getUserFeedback(),
                    LocalDateTime.now(),
                    request.getFeedbackComment()
            );

            if (result <= 0) {
                return Result.error("更新用户反馈失败");
            }

            // 查询更新后的记录
            FormFillRecord updatedRecord = formFillRecordMapper.selectByIdWithDetails(request.getRecordId());
            FormFillRecordVO vo = convertToVO(updatedRecord);

            log.info("更新用户反馈成功: recordId={}, userId={}, feedback={}", 
                    request.getRecordId(), currentUser.getId(), request.getUserFeedback());

            return Result.success(vo);

        } catch (Exception e) {
            log.error("更新用户反馈失败", e);
            return Result.error("更新用户反馈失败: " + e.getMessage());
        }
    }

    @Override
    public Result<FormFillRecordVO> getFillRecordById(Long id) {
        try {
            FormFillRecord record = formFillRecordMapper.selectByIdWithDetails(id);
            if (record == null) {
                return Result.error(ResultCode.NOT_FOUND, "回填记录不存在");
            }

            FormFillRecordVO vo = convertToVO(record);
            return Result.success(vo);

        } catch (Exception e) {
            log.error("查询回填记录失败: id={}", id, e);
            return Result.error("查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<FormFillRecordVO>> getFillRecordPage(Integer page, Integer size, 
                                                                 String keyword, Integer fillResult, 
                                                                 Integer userFeedback, Long userId,
                                                                 LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Page<FormFillRecord> pageParam = new Page<>(page, size);
            IPage<FormFillRecord> pageResult = formFillRecordMapper.selectPageWithDetails(
                    pageParam, keyword, fillResult, userFeedback, userId, startTime, endTime);

            List<FormFillRecordVO> voList = pageResult.getRecords().stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            PageResult<FormFillRecordVO> result = PageResult.of(
                    voList, pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize());

            return Result.success(result);

        } catch (Exception e) {
            log.error("分页查询回填记录失败", e);
            return Result.error("分页查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FormFillRecordVO>> getFillRecordsByUserId(Long userId) {
        try {
            List<FormFillRecord> records = formFillRecordMapper.findByUserId(userId);
            List<FormFillRecordVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            return Result.success(voList);

        } catch (Exception e) {
            log.error("根据用户ID查询回填记录失败: userId={}", userId, e);
            return Result.error("查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FormFillRecordVO>> getFillRecordsByRecognitionRecordId(Long recognitionRecordId) {
        try {
            List<FormFillRecord> records = formFillRecordMapper.findByRecognitionRecordId(recognitionRecordId);
            List<FormFillRecordVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            return Result.success(voList);

        } catch (Exception e) {
            log.error("根据识别记录ID查询回填记录失败: recognitionRecordId={}", recognitionRecordId, e);
            return Result.error("查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FormFillRecordVO>> getFillRecordsByPageBindingId(Long pageBindingId) {
        try {
            List<FormFillRecord> records = formFillRecordMapper.findByPageBindingId(pageBindingId);
            List<FormFillRecordVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            return Result.success(voList);

        } catch (Exception e) {
            log.error("根据页面绑定ID查询回填记录失败: pageBindingId={}", pageBindingId, e);
            return Result.error("查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<FormFillStatisticsVO> getFillStatistics() {
        try {
            FormFillStatisticsVO statistics = new FormFillStatisticsVO();

            // 基础统计
            Long totalRecords = formFillRecordMapper.countRecords();
            Long successRecords = formFillRecordMapper.countSuccessRecords();
            Double successRate = totalRecords > 0 ? (successRecords * 100.0 / totalRecords) : 0.0;

            statistics.setTotalRecords(totalRecords);
            statistics.setSuccessRecords(successRecords);
            statistics.setSuccessRate(Math.round(successRate * 100.0) / 100.0);

            // 各类统计
            statistics.setFillResultStats(formFillRecordMapper.countByFillResult());
            statistics.setUserFeedbackStats(formFillRecordMapper.countByUserFeedback());
            statistics.setUserFillStats(formFillRecordMapper.countByUser());
            statistics.setPageBindingFillStats(formFillRecordMapper.countByPageBinding());
            statistics.setPopularFillPages(formFillRecordMapper.findPopularFillPages(10));
            statistics.setUserFillActivity(formFillRecordMapper.getUserFillActivity(30));

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取回填统计信息失败", e);
            return Result.error("获取回填统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<FormFillStatisticsVO> getUserFillStatistics(Long userId) {
        try {
            FormFillStatisticsVO statistics = new FormFillStatisticsVO();

            // 用户回填成功率
            Map<String, Object> userStats = formFillRecordMapper.getUserFillSuccessRate(userId);
            if (userStats != null) {
                statistics.setTotalRecords(((Number) userStats.get("totalCount")).longValue());
                statistics.setSuccessRecords(((Number) userStats.get("successCount")).longValue());
                statistics.setSuccessRate(((Number) userStats.get("successRate")).doubleValue());
            }

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取用户回填统计信息失败: userId={}", userId, e);
            return Result.error("获取用户回填统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<FormFillStatisticsVO> getPageBindingFillStatistics(Long pageBindingId) {
        try {
            FormFillStatisticsVO statistics = new FormFillStatisticsVO();

            // 页面绑定回填成功率
            Map<String, Object> bindingStats = formFillRecordMapper.getPageBindingFillSuccessRate(pageBindingId);
            if (bindingStats != null) {
                statistics.setTotalRecords(((Number) bindingStats.get("totalCount")).longValue());
                statistics.setSuccessRecords(((Number) bindingStats.get("successCount")).longValue());
                statistics.setSuccessRate(((Number) bindingStats.get("successRate")).doubleValue());
            }

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取页面绑定回填统计信息失败: pageBindingId={}", pageBindingId, e);
            return Result.error("获取页面绑定回填统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FormFillRecordVO>> getRecentFillRecords(Integer limit) {
        try {
            List<FormFillRecord> records = formFillRecordMapper.findRecentRecords(limit);
            List<FormFillRecordVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            return Result.success(voList);

        } catch (Exception e) {
            log.error("查询最近回填记录失败: limit={}", limit, e);
            return Result.error("查询最近回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FormFillRecordVO>> getFillRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<FormFillRecord> records = formFillRecordMapper.findByTimeRange(startTime, endTime);
            List<FormFillRecordVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            return Result.success(voList);

        } catch (Exception e) {
            log.error("根据时间范围查询回填记录失败: startTime={}, endTime={}", startTime, endTime, e);
            return Result.error("查询回填记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteFillRecord(Long id) {
        try {
            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();
            if (currentUser == null) {
                return Result.error(ResultCode.UNAUTHORIZED, "用户未登录");
            }

            // 检查记录是否存在
            FormFillRecord existingRecord = formFillRecordMapper.selectById(id);
            if (existingRecord == null) {
                return Result.error(ResultCode.NOT_FOUND, "回填记录不存在");
            }

            // 逻辑删除
            int result = formFillRecordMapper.deleteById(id);
            if (result <= 0) {
                return Result.error("删除回填记录失败");
            }

            log.info("删除回填记录成功: recordId={}, userId={}", id, currentUser.getId());
            return Result.success();

        } catch (Exception e) {
            log.error("删除回填记录失败: id={}", id, e);
            return Result.error("删除回填记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchDeleteFillRecords(List<Long> ids) {
        try {
            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();
            if (currentUser == null) {
                return Result.error(ResultCode.UNAUTHORIZED, "用户未登录");
            }

            // 批量删除
            int result = formFillRecordMapper.deleteBatchIds(ids);
            if (result <= 0) {
                return Result.error("批量删除回填记录失败");
            }

            log.info("批量删除回填记录成功: count={}, userId={}", result, currentUser.getId());
            return Result.success();

        } catch (Exception e) {
            log.error("批量删除回填记录失败: ids={}", ids, e);
            return Result.error("批量删除回填记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Double> getUserFillSuccessRate(Long userId) {
        try {
            Map<String, Object> stats = formFillRecordMapper.getUserFillSuccessRate(userId);
            Double successRate = stats != null ? ((Number) stats.get("successRate")).doubleValue() : 0.0;
            return Result.success(successRate);

        } catch (Exception e) {
            log.error("获取用户回填成功率失败: userId={}", userId, e);
            return Result.error("获取用户回填成功率失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Double> getPageBindingFillSuccessRate(Long pageBindingId) {
        try {
            Map<String, Object> stats = formFillRecordMapper.getPageBindingFillSuccessRate(pageBindingId);
            Double successRate = stats != null ? ((Number) stats.get("successRate")).doubleValue() : 0.0;
            return Result.success(successRate);

        } catch (Exception e) {
            log.error("获取页面绑定回填成功率失败: pageBindingId={}", pageBindingId, e);
            return Result.error("获取页面绑定回填成功率失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        try {
            return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e) {
            log.warn("获取当前用户失败", e);
            return null;
        }
    }

    /**
     * 转换为VO对象
     */
    private FormFillRecordVO convertToVO(FormFillRecord record) {
        FormFillRecordVO vo = new FormFillRecordVO();
        BeanUtils.copyProperties(record, vo);
        
        // 设置描述字段
        vo.setFillResultDesc(record.getFillResultDesc());
        vo.setUserFeedbackDesc(record.getUserFeedbackDesc());
        
        return vo;
    }
}
