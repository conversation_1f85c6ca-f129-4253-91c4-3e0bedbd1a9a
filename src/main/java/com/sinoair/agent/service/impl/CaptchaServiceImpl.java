package com.sinoair.agent.service.impl;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.sinoair.agent.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 图形验证码服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements CaptchaService {

    @Autowired
    private DefaultKaptcha defaultKaptcha;

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String CAPTCHA_PREFIX = "captcha:";
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    @Override
    public Map<String, String> generateCaptcha() {
        try {
            // 生成验证码文本
            String captchaText = defaultKaptcha.createText();
            
            // 生成验证码图片
            BufferedImage captchaImage = defaultKaptcha.createImage(captchaText);
            
            // 将图片转换为Base64
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(captchaImage, "jpg", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            
            // 生成唯一ID
            String captchaId = UUID.randomUUID().toString().replace("-", "");
            
            // 存储验证码到Redis
            String key = CAPTCHA_PREFIX + captchaId;
            redisTemplate.opsForValue().set(key, captchaText.toUpperCase(), CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 返回结果
            Map<String, String> result = new HashMap<>();
            result.put("captchaId", captchaId);
            result.put("captchaImage", "data:image/jpeg;base64," + base64Image);
            
            log.debug("生成图形验证码成功，ID：{}，验证码：{}", captchaId, captchaText);
            return result;
            
        } catch (IOException e) {
            log.error("生成图形验证码失败", e);
            throw new RuntimeException("生成图形验证码失败", e);
        }
    }

    @Override
    public boolean verifyCaptcha(String captchaId, String captchaCode) {
        if (captchaId == null || captchaCode == null) {
            return false;
        }
        
        try {
            String key = CAPTCHA_PREFIX + captchaId;
            String storedCode = redisTemplate.opsForValue().get(key);
            
            if (storedCode != null && storedCode.equalsIgnoreCase(captchaCode.trim())) {
                // 验证成功后删除验证码（一次性使用）
                redisTemplate.delete(key);
                log.debug("图形验证码验证成功，ID：{}", captchaId);
                return true;
            }
            
            log.debug("图形验证码验证失败，ID：{}，输入：{}，存储：{}", captchaId, captchaCode, storedCode);
            return false;
            
        } catch (Exception e) {
            log.error("验证图形验证码异常，ID：{}", captchaId, e);
            return false;
        }
    }

    @Override
    public void clearCaptcha(String captchaId) {
        if (captchaId != null) {
            String key = CAPTCHA_PREFIX + captchaId;
            redisTemplate.delete(key);
            log.debug("清除图形验证码，ID：{}", captchaId);
        }
    }
}
