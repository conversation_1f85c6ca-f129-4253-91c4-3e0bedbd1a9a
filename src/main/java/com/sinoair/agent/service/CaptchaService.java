package com.sinoair.agent.service;

import java.util.Map;

/**
 * 图形验证码服务接口
 *
 * <AUTHOR> Team
 */
public interface CaptchaService {

    /**
     * 生成图形验证码
     *
     * @return 包含验证码ID和Base64图片的Map
     */
    Map<String, String> generateCaptcha();

    /**
     * 验证图形验证码
     *
     * @param captchaId 验证码ID
     * @param captchaCode 用户输入的验证码
     * @return 验证是否成功
     */
    boolean verifyCaptcha(String captchaId, String captchaCode);

    /**
     * 清除验证码
     *
     * @param captchaId 验证码ID
     */
    void clearCaptcha(String captchaId);
}
