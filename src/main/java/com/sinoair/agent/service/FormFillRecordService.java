package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.FormFillFeedbackRequest;
import com.sinoair.agent.dto.request.FormFillRecordRequest;
import com.sinoair.agent.dto.response.FormFillRecordVO;
import com.sinoair.agent.dto.response.FormFillStatisticsVO;
import com.sinoair.agent.entity.FormFillRecord;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 表单回填记录服务接口
 * 
 * <AUTHOR> Team
 */
public interface FormFillRecordService {

    /**
     * 创建回填记录
     */
    Result<FormFillRecordVO> createFillRecord(FormFillRecordRequest request);

    /**
     * 更新用户反馈
     */
    Result<FormFillRecordVO> updateUserFeedback(FormFillFeedbackRequest request);

    /**
     * 根据ID获取回填记录详情
     */
    Result<FormFillRecordVO> getFillRecordById(Long id);

    /**
     * 分页查询回填记录
     */
    Result<PageResult<FormFillRecordVO>> getFillRecordPage(Integer page, Integer size, 
                                                          String keyword, Integer fillResult, 
                                                          Integer userFeedback, Long userId,
                                                          LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID查询回填记录
     */
    Result<List<FormFillRecordVO>> getFillRecordsByUserId(Long userId);

    /**
     * 根据识别记录ID查询回填记录
     */
    Result<List<FormFillRecordVO>> getFillRecordsByRecognitionRecordId(Long recognitionRecordId);

    /**
     * 根据页面绑定ID查询回填记录
     */
    Result<List<FormFillRecordVO>> getFillRecordsByPageBindingId(Long pageBindingId);

    /**
     * 获取回填统计信息
     */
    Result<FormFillStatisticsVO> getFillStatistics();

    /**
     * 获取用户回填统计信息
     */
    Result<FormFillStatisticsVO> getUserFillStatistics(Long userId);

    /**
     * 获取页面绑定回填统计信息
     */
    Result<FormFillStatisticsVO> getPageBindingFillStatistics(Long pageBindingId);

    /**
     * 获取最近的回填记录
     */
    Result<List<FormFillRecordVO>> getRecentFillRecords(Integer limit);

    /**
     * 根据时间范围查询回填记录
     */
    Result<List<FormFillRecordVO>> getFillRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除回填记录
     */
    Result<Void> deleteFillRecord(Long id);

    /**
     * 批量删除回填记录
     */
    Result<Void> batchDeleteFillRecords(List<Long> ids);

    /**
     * 获取用户回填成功率
     */
    Result<Double> getUserFillSuccessRate(Long userId);

    /**
     * 获取页面绑定回填成功率
     */
    Result<Double> getPageBindingFillSuccessRate(Long pageBindingId);
}
