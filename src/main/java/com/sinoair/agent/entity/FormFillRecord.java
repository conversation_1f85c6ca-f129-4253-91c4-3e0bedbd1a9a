package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 表单回填记录实体类
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("form_fill_records")
public class FormFillRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的识别记录ID，NULL表示使用JSON数据
     */
    @TableField("recognition_record_id")
    private Long recognitionRecordId;

    /**
     * 页面绑定配置ID
     */
    @TableField("page_binding_id")
    private Long pageBindingId;

    /**
     * 目标页面URL
     */
    @TableField("target_url")
    private String targetUrl;

    /**
     * 回填的数据内容（JSON格式）
     */
    @JsonRawValue
    @TableField("fill_data")
    private String fillData;

    /**
     * 回填结果：1-成功，2-部分成功，3-失败
     */
    @TableField("fill_result")
    private Integer fillResult;

    /**
     * 错误信息（回填失败时记录）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行回填的用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户反馈：1-点赞，0-踩，NULL-无反馈
     */
    @TableField("user_feedback")
    private Integer userFeedback;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("feedback_time")
    private LocalDateTime feedbackTime;

    /**
     * 反馈备注
     */
    @TableField("feedback_comment")
    private String feedbackComment;

    /**
     * 回填时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("fill_time")
    private LocalDateTime fillTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 删除标记
     */
    @TableLogic
    private Integer deleted;

    // 关联查询字段（非数据库字段）
    
    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户真实姓名
     */
    @TableField(exist = false)
    private String userRealName;

    /**
     * 页面绑定名称
     */
    @TableField(exist = false)
    private String bindingName;

    /**
     * 识别记录信息
     */
    @TableField(exist = false)
    private RecognitionRecord recognitionRecord;

    /**
     * 页面绑定信息
     */
    @TableField(exist = false)
    private PageBinding pageBinding;

    // 状态常量
    public static final int FILL_RESULT_SUCCESS = 1;        // 成功
    public static final int FILL_RESULT_PARTIAL_SUCCESS = 2; // 部分成功
    public static final int FILL_RESULT_FAILED = 3;         // 失败

    // 反馈常量
    public static final int FEEDBACK_LIKE = 1;    // 点赞
    public static final int FEEDBACK_DISLIKE = 0; // 踩

    /**
     * 获取回填结果描述
     */
    public String getFillResultDesc() {
        if (fillResult == null) {
            return "未知";
        }
        switch (fillResult) {
            case FILL_RESULT_SUCCESS:
                return "成功";
            case FILL_RESULT_PARTIAL_SUCCESS:
                return "部分成功";
            case FILL_RESULT_FAILED:
                return "失败";
            default:
                return "未知";
        }
    }

    /**
     * 获取用户反馈描述
     */
    public String getUserFeedbackDesc() {
        if (userFeedback == null) {
            return "无反馈";
        }
        switch (userFeedback) {
            case FEEDBACK_LIKE:
                return "点赞";
            case FEEDBACK_DISLIKE:
                return "踩";
            default:
                return "无反馈";
        }
    }
}
