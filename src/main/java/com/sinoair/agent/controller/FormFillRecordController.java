package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.FormFillFeedbackRequest;
import com.sinoair.agent.dto.request.FormFillRecordRequest;
import com.sinoair.agent.dto.response.FormFillRecordVO;
import com.sinoair.agent.dto.response.FormFillStatisticsVO;
import com.sinoair.agent.service.FormFillRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;


import java.time.LocalDateTime;
import java.util.List;

/**
 * 表单回填记录控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "表单回填记录", description = "表单回填记录管理相关功能")
@RestController
@RequestMapping("/api/v1/form-fill-records")
@RequiredArgsConstructor
@Slf4j
public class FormFillRecordController {

    private final FormFillRecordService formFillRecordService;

    @Operation(summary = "创建回填记录", description = "创建新的表单回填记录")
    @PostMapping
    @OperationLogRecord(module = "表单回填", operationType = "CREATE", operationDesc = "创建回填记录")
    public Result<FormFillRecordVO> createFillRecord(@RequestBody FormFillRecordRequest request) {
        return formFillRecordService.createFillRecord(request);
    }

    @Operation(summary = "更新用户反馈", description = "更新回填记录的用户反馈（点赞/踩）")
    @PutMapping("/feedback")
    @OperationLogRecord(module = "表单回填", operationType = "UPDATE", operationDesc = "更新用户反馈")
    public Result<FormFillRecordVO> updateUserFeedback(@RequestBody FormFillFeedbackRequest request) {
        return formFillRecordService.updateUserFeedback(request);
    }

    @Operation(summary = "获取回填记录详情", description = "根据ID获取回填记录详情")
    @GetMapping("/{id}")
    public Result<FormFillRecordVO> getFillRecordById(
            @Parameter(description = "回填记录ID") @PathVariable Long id) {
        return formFillRecordService.getFillRecordById(id);
    }

    @Operation(summary = "分页查询回填记录", description = "分页查询回填记录列表")
    @GetMapping("/page")
    public Result<PageResult<FormFillRecordVO>> getFillRecordPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "回填结果：1-成功，2-部分成功，3-失败") @RequestParam(required = false) Integer fillResult,
            @Parameter(description = "用户反馈：1-点赞，0-踩") @RequestParam(required = false) Integer userFeedback,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        return formFillRecordService.getFillRecordPage(page, size, keyword, fillResult, 
                userFeedback, userId, startTime, endTime);
    }

    @Operation(summary = "根据用户ID查询回填记录", description = "获取指定用户的所有回填记录")
    @GetMapping("/user/{userId}")
    public Result<List<FormFillRecordVO>> getFillRecordsByUserId(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return formFillRecordService.getFillRecordsByUserId(userId);
    }

    @Operation(summary = "根据识别记录ID查询回填记录", description = "获取指定识别记录的所有回填记录")
    @GetMapping("/recognition/{recognitionRecordId}")
    public Result<List<FormFillRecordVO>> getFillRecordsByRecognitionRecordId(
            @Parameter(description = "识别记录ID") @PathVariable Long recognitionRecordId) {
        return formFillRecordService.getFillRecordsByRecognitionRecordId(recognitionRecordId);
    }

    @Operation(summary = "根据页面绑定ID查询回填记录", description = "获取指定页面绑定的所有回填记录")
    @GetMapping("/page-binding/{pageBindingId}")
    public Result<List<FormFillRecordVO>> getFillRecordsByPageBindingId(
            @Parameter(description = "页面绑定ID") @PathVariable Long pageBindingId) {
        return formFillRecordService.getFillRecordsByPageBindingId(pageBindingId);
    }

    @Operation(summary = "获取回填统计信息", description = "获取整体回填统计信息")
    @GetMapping("/statistics")
    public Result<FormFillStatisticsVO> getFillStatistics() {
        return formFillRecordService.getFillStatistics();
    }

    @Operation(summary = "获取用户回填统计", description = "获取指定用户的回填统计信息")
    @GetMapping("/statistics/user/{userId}")
    public Result<FormFillStatisticsVO> getUserFillStatistics(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return formFillRecordService.getUserFillStatistics(userId);
    }

    @Operation(summary = "获取页面绑定回填统计", description = "获取指定页面绑定的回填统计信息")
    @GetMapping("/statistics/page-binding/{pageBindingId}")
    public Result<FormFillStatisticsVO> getPageBindingFillStatistics(
            @Parameter(description = "页面绑定ID") @PathVariable Long pageBindingId) {
        return formFillRecordService.getPageBindingFillStatistics(pageBindingId);
    }

    @Operation(summary = "获取最近回填记录", description = "获取最近的回填记录")
    @GetMapping("/recent")
    public Result<List<FormFillRecordVO>> getRecentFillRecords(
            @Parameter(description = "记录数量") @RequestParam(defaultValue = "10") Integer limit) {
        return formFillRecordService.getRecentFillRecords(limit);
    }

    @Operation(summary = "根据时间范围查询回填记录", description = "根据时间范围查询回填记录")
    @GetMapping("/time-range")
    public Result<List<FormFillRecordVO>> getFillRecordsByTimeRange(
            @Parameter(description = "开始时间") @RequestParam 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        return formFillRecordService.getFillRecordsByTimeRange(startTime, endTime);
    }

    @Operation(summary = "删除回填记录", description = "删除指定的回填记录")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "表单回填", operationType = "DELETE", operationDesc = "删除回填记录")
    public Result<Void> deleteFillRecord(
            @Parameter(description = "回填记录ID") @PathVariable Long id) {
        return formFillRecordService.deleteFillRecord(id);
    }

    @Operation(summary = "批量删除回填记录", description = "批量删除回填记录")
    @DeleteMapping("/batch")
    @OperationLogRecord(module = "表单回填", operationType = "DELETE", operationDesc = "批量删除回填记录")
    public Result<Void> batchDeleteFillRecords(@RequestBody List<Long> ids) {
        return formFillRecordService.batchDeleteFillRecords(ids);
    }

    @Operation(summary = "获取用户回填成功率", description = "获取指定用户的回填成功率")
    @GetMapping("/success-rate/user/{userId}")
    public Result<Double> getUserFillSuccessRate(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        return formFillRecordService.getUserFillSuccessRate(userId);
    }

    @Operation(summary = "获取页面绑定回填成功率", description = "获取指定页面绑定的回填成功率")
    @GetMapping("/success-rate/page-binding/{pageBindingId}")
    public Result<Double> getPageBindingFillSuccessRate(
            @Parameter(description = "页面绑定ID") @PathVariable Long pageBindingId) {
        return formFillRecordService.getPageBindingFillSuccessRate(pageBindingId);
    }
}
