package com.sinoair.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.dto.response.UserSubscriptionDTO;
import com.sinoair.agent.entity.CallHistory;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.CallHistoryService;
import com.sinoair.agent.service.UserAgentSubscriptionService;
import com.sinoair.agent.service.UserService;
import com.sinoair.agent.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Vue仪表盘API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VueDashboardController {

    @Autowired
    private CallHistoryService callHistoryService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private UserAgentSubscriptionService userAgentSubscriptionService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * 从Token中获取用户信息
     */
    private User getUserFromToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token == null || !jwtTokenProvider.validateToken(token)) {
            return null;
        }

        try {
            Long userId = jwtTokenProvider.getUserIdFromToken(token);
            UserVO userVO = userService.getUserById(userId);
            if (userVO != null) {
                // 将UserVO转换为User实体
                return convertUserVOToUser(userVO);
            }
            return null;
        } catch (Exception e) {
            log.error("从Token获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 将UserVO转换为User实体
     */
    private User convertUserVOToUser(UserVO userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setUsername(userVO.getUsername());
        user.setEmail(userVO.getEmail());
        user.setRealName(userVO.getRealName());
        user.setLastLoginTime(userVO.getLastLoginTime());
        return user;
    }

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/stats")
    public Map<String, Object> getDashboardStats(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            // 获取订阅数据
            int subscriptionCount = userAgentSubscriptionService.getUserActiveSubscriptionCount(user.getId());
            List<UserSubscriptionDTO> activeSubscriptions = userAgentSubscriptionService.getUserActiveSubscriptions(user.getId());

            // 获取调用统计数据
            Long monthlyCallCount = callHistoryService.getMonthlyCallCount(user.getId());
            Long todayCallCount = callHistoryService.getTodayCallCount(user.getId());
            Double successRate = callHistoryService.getSuccessRate(user.getId());

            // 构建统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("subscriptions", subscriptionCount);
            stats.put("monthlyCalls", monthlyCallCount);
            stats.put("todayCalls", todayCallCount);
            stats.put("remainingCalls", 8766); // 暂时保留模拟数据
            stats.put("successRate", Double.parseDouble(String.format("%.1f", successRate)));

            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("user", Map.of(
                "id", user.getId(),
                "email", user.getEmail(),
                "realName", user.getRealName() != null ? user.getRealName() : user.getUsername(),
                "lastLoginTime", user.getLastLoginTime()
            ));
            data.put("stats", stats);
            data.put("activeSubscriptions", activeSubscriptions);

            response.put("success", true);
            response.put("data", data);
            response.put("code", 200);

            log.info("仪表盘统计数据获取成功，用户：{}，订阅数：{}，本月调用：{}，今日调用：{}，成功率：{}%",
                    user.getId(), subscriptionCount, monthlyCallCount, todayCallCount, successRate);

        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取统计数据失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取最近调用记录
     */
    @GetMapping("/recent-calls")
    public Map<String, Object> getRecentCalls(HttpServletRequest request,
                                             @RequestParam(defaultValue = "5") Integer limit) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            List<CallHistory> recentCalls = callHistoryService.getRecentCallHistory(user.getId(), limit);
            
            response.put("success", true);
            response.put("data", recentCalls);
            response.put("code", 200);

            log.info("获取用户{}最近调用记录成功，记录数：{}", user.getId(), recentCalls.size());

        } catch (Exception e) {
            log.error("获取用户{}最近调用记录失败", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取调用记录失败");
            response.put("code", 500);
            response.put("data", new ArrayList<>());
        }

        return response;
    }

    /**
     * 获取调用历史（分页）
     */
    @GetMapping("/call-history")
    public Map<String, Object> getCallHistory(HttpServletRequest request,
                                            @RequestParam(defaultValue = "1") Integer page,
                                            @RequestParam(defaultValue = "10") Integer size,
                                            @RequestParam(required = false) String agentName,
                                            @RequestParam(required = false) String callType) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            Page<CallHistory> pageObj = new Page<>(page, size);
            IPage<CallHistory> historyPage = callHistoryService.getCallHistoryByUserId(
                user.getId(), pageObj, agentName, callType, null, null);

            // 计算统计信息
            long successCount = callHistoryService.getSuccessCountByUserId(user.getId());
            long failedCount = callHistoryService.getFailedCountByUserId(user.getId());

            Map<String, Object> data = new HashMap<>();
            data.put("records", historyPage.getRecords());
            data.put("total", historyPage.getTotal());
            data.put("pages", historyPage.getPages());
            data.put("current", historyPage.getCurrent());
            data.put("size", historyPage.getSize());
            data.put("successCount", successCount);
            data.put("failedCount", failedCount);

            response.put("success", true);
            response.put("data", data);
            response.put("code", 200);

        } catch (Exception e) {
            log.error("查询调用历史失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "查询调用历史失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取调用历史详情
     */
    @GetMapping("/call-history/{id}")
    public Map<String, Object> getCallHistoryDetail(@PathVariable Long id, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            CallHistory history = callHistoryService.getCallHistoryById(id, user.getId());
            if (history != null) {
                response.put("success", true);
                response.put("data", history);
                response.put("code", 200);
            } else {
                response.put("success", false);
                response.put("message", "调用记录不存在或无权限访问");
                response.put("code", 404);
            }
        } catch (Exception e) {
            log.error("获取调用历史详情失败，ID：{}，用户：{}", id, user.getId(), e);
            response.put("success", false);
            response.put("message", "获取详情失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取用户订阅信息
     */
    @GetMapping("/subscriptions")
    public Map<String, Object> getUserSubscriptions(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            List<UserSubscriptionDTO> userSubscriptions = userAgentSubscriptionService.getUserSubscriptions(user.getId());
            
            response.put("success", true);
            response.put("data", userSubscriptions);
            response.put("code", 200);

        } catch (Exception e) {
            log.error("获取用户订阅信息失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取订阅信息失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 下载插件文件 - 动态打包chrome-extension文件夹
     */
    @GetMapping("/download-plugin")
    public ResponseEntity<Resource> downloadPlugin(HttpServletRequest request) {
        User user = getUserFromToken(request);
        if (user == null) {
            return ResponseEntity.status(401).build();
        }

        try {
            // 获取chrome-extension文件夹路径 - 支持多种路径
            Path chromeExtensionPath = null;

            // 尝试多个可能的路径
            String[] possiblePaths = {
                "chrome-extension",           // 相对路径
                "./chrome-extension",         // 当前目录
                "src/main/resources/static/chrome-extension", // 资源目录
                "/app/chrome-extension"       // Docker容器路径
            };

            for (String pathStr : possiblePaths) {
                Path testPath = Paths.get(pathStr);
                if (Files.exists(testPath)) {
                    chromeExtensionPath = testPath;
                    log.info("找到chrome-extension文件夹: {}", testPath.toAbsolutePath());
                    break;
                }
            }

            if (chromeExtensionPath == null || !Files.exists(chromeExtensionPath)) {
                log.error("chrome-extension文件夹不存在，已尝试路径: {}", String.join(", ", possiblePaths));
                log.error("当前工作目录: {}", System.getProperty("user.dir"));
                return ResponseEntity.notFound().build();
            }

            // 创建内存中的ZIP文件
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (ZipOutputStream zos = new ZipOutputStream(baos)) {
                // 递归添加文件夹中的所有文件到ZIP
                addDirectoryToZip(chromeExtensionPath, chromeExtensionPath, zos);
            }

            // 创建ByteArrayResource
            ByteArrayResource resource = new ByteArrayResource(baos.toByteArray());

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"sinoair-agent-chrome-plugin.zip\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            log.info("用户 {} 下载Chrome插件包成功，文件大小: {} bytes", user.getEmail(), baos.size());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            log.error("打包下载Chrome插件失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 递归添加目录中的文件到ZIP
     */
    private void addDirectoryToZip(Path sourceDir, Path baseDir, ZipOutputStream zos) throws IOException {
        Files.walk(sourceDir)
                .filter(path -> !Files.isDirectory(path))
                .forEach(path -> {
                    try {
                        // 计算相对路径
                        Path relativePath = baseDir.relativize(path);
                        String zipEntryName = relativePath.toString().replace("\\", "/");

                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(zipEntryName);
                        zos.putNextEntry(zipEntry);

                        // 写入文件内容
                        Files.copy(path, zos);
                        zos.closeEntry();

                        log.debug("添加文件到ZIP: {}", zipEntryName);
                    } catch (IOException e) {
                        log.error("添加文件到ZIP失败: {}", path, e);
                    }
                });
    }
}
