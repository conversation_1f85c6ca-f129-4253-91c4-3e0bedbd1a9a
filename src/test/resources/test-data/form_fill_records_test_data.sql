-- 表单回填记录测试数据
-- 用于测试表单回填记录功能

-- 插入测试用的回填记录
INSERT INTO form_fill_records (
    recognition_record_id, 
    page_binding_id, 
    target_url, 
    fill_data, 
    fill_result, 
    error_message, 
    user_id, 
    user_feedback, 
    feedback_time, 
    feedback_comment, 
    fill_time, 
    created_time, 
    updated_time
) VALUES 
-- 成功的回填记录，有点赞反馈
(1, 1, 'https://example.com/register', 
 '{"name":"张三","email":"z<PERSON><PERSON>@example.com","phone":"13800138000","age":30}', 
 1, NULL, 1, 1, '2024-01-20 10:35:00', '回填效果很好，节省了很多时间！', 
 '2024-01-20 10:30:00', '2024-01-20 10:30:00', '2024-01-20 10:35:00'),

-- 部分成功的回填记录，有踩的反馈
(2, 1, 'https://example.com/register', 
 '{"name":"李四","email":"<EMAIL>","phone":"13900139000"}', 
 2, '部分字段填写失败：年龄字段未找到', 1, 0, '2024-01-20 11:20:00', '有些字段没有填上', 
 '2024-01-20 11:15:00', '2024-01-20 11:15:00', '2024-01-20 11:20:00'),

-- 失败的回填记录，无反馈
(3, 2, 'https://example.com/contact', 
 '{"name":"王五","message":"测试消息"}', 
 3, '页面结构发生变化，无法找到表单元素', 1, NULL, NULL, NULL, 
 '2024-01-20 12:00:00', '2024-01-20 12:00:00', '2024-01-20 12:00:00'),

-- 成功的回填记录，无反馈
(4, 1, 'https://example.com/register', 
 '{"name":"赵六","email":"<EMAIL>","phone":"13700137000","age":25}', 
 1, NULL, 2, NULL, NULL, NULL, 
 '2024-01-20 14:30:00', '2024-01-20 14:30:00', '2024-01-20 14:30:00'),

-- 使用JSON数据的回填记录（recognition_record_id为-1）
(-1, 1, 'https://example.com/register', 
 '{"name":"孙七","email":"<EMAIL>","phone":"13600136000","age":28}', 
 1, NULL, 1, 1, '2024-01-20 15:45:00', '使用JSON数据回填也很方便', 
 '2024-01-20 15:40:00', '2024-01-20 15:40:00', '2024-01-20 15:45:00'),

-- 多个用户的回填记录
(5, 3, 'https://example.com/feedback', 
 '{"rating":"5","comment":"很好的产品"}', 
 1, NULL, 2, 1, '2024-01-20 16:10:00', '反馈表单填写成功', 
 '2024-01-20 16:05:00', '2024-01-20 16:05:00', '2024-01-20 16:10:00'),

-- 不同页面绑定的回填记录
(6, 4, 'https://example.com/survey', 
 '{"question1":"选项A","question2":"选项B","question3":"非常满意"}', 
 2, '部分选项未能正确选择', 2, 0, '2024-01-20 17:25:00', '有些选项没有选中', 
 '2024-01-20 17:20:00', '2024-01-20 17:20:00', '2024-01-20 17:25:00'),

-- 最近的回填记录
(7, 1, 'https://example.com/register', 
 '{"name":"周八","email":"<EMAIL>","phone":"13500135000","age":32}', 
 1, NULL, 1, NULL, NULL, NULL, 
 '2024-01-21 09:15:00', '2024-01-21 09:15:00', '2024-01-21 09:15:00'),

(8, 2, 'https://example.com/contact', 
 '{"name":"吴九","email":"<EMAIL>","subject":"咨询问题","message":"请问如何使用这个功能？"}', 
 1, NULL, 2, 1, '2024-01-21 10:30:00', '联系表单填写很准确', 
 '2024-01-21 10:25:00', '2024-01-21 10:25:00', '2024-01-21 10:30:00'),

-- 今天的回填记录
(9, 1, 'https://example.com/register', 
 '{"name":"郑十","email":"<EMAIL>","phone":"13400134000","age":27}', 
 1, NULL, 1, 1, NOW(), '今天的回填测试', 
 NOW() - INTERVAL 5 MINUTE, NOW() - INTERVAL 5 MINUTE, NOW());

-- 验证数据插入
SELECT 
    ffr.id,
    ffr.recognition_record_id,
    pb.binding_name,
    ffr.target_url,
    ffr.fill_result,
    CASE 
        WHEN ffr.fill_result = 1 THEN '成功'
        WHEN ffr.fill_result = 2 THEN '部分成功'
        WHEN ffr.fill_result = 3 THEN '失败'
        ELSE '未知'
    END as fill_result_desc,
    ffr.user_feedback,
    CASE 
        WHEN ffr.user_feedback IS NULL THEN '无反馈'
        WHEN ffr.user_feedback = 1 THEN '点赞'
        WHEN ffr.user_feedback = 0 THEN '踩'
        ELSE '未知'
    END as feedback_desc,
    u.username,
    ffr.fill_time
FROM form_fill_records ffr
LEFT JOIN page_bindings pb ON ffr.page_binding_id = pb.id
LEFT JOIN sys_user u ON ffr.user_id = u.id
WHERE ffr.deleted = 0
ORDER BY ffr.fill_time DESC;
