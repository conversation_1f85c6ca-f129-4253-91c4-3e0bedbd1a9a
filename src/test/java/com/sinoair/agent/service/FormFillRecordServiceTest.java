package com.sinoair.agent.service;

import com.sinoair.agent.dto.request.FormFillFeedbackRequest;
import com.sinoair.agent.dto.request.FormFillRecordRequest;
import com.sinoair.agent.dto.response.FormFillRecordVO;
import com.sinoair.agent.entity.FormFillRecord;
import com.sinoair.agent.mapper.FormFillRecordMapper;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.impl.FormFillRecordServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 表单回填记录服务测试类
 * 
 * <AUTHOR> Team
 */
@ExtendWith(MockitoExtension.class)
class FormFillRecordServiceTest {

    @Mock
    private FormFillRecordMapper formFillRecordMapper;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private FormFillRecordServiceImpl formFillRecordService;

    private UserPrincipal mockUser;
    private FormFillRecordRequest mockRequest;
    private FormFillRecord mockRecord;

    @BeforeEach
    void setUp() {
        // 设置模拟用户
        mockUser = new UserPrincipal();
        mockUser.setId(1L);
        mockUser.setUsername("testuser");
        mockUser.setRealName("测试用户");

        // 设置Security Context
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(mockUser);
        SecurityContextHolder.setContext(securityContext);

        // 设置模拟请求
        mockRequest = new FormFillRecordRequest();
        mockRequest.setRecognitionRecordId(1L);
        mockRequest.setPageBindingId(1L);
        mockRequest.setTargetUrl("https://example.com/form");
        mockRequest.setFillData("{\"name\":\"张三\",\"age\":30}");
        mockRequest.setFillResult(FormFillRecord.FILL_RESULT_SUCCESS);

        // 设置模拟记录
        mockRecord = new FormFillRecord();
        mockRecord.setId(1L);
        mockRecord.setRecognitionRecordId(1L);
        mockRecord.setPageBindingId(1L);
        mockRecord.setTargetUrl("https://example.com/form");
        mockRecord.setFillData("{\"name\":\"张三\",\"age\":30}");
        mockRecord.setFillResult(FormFillRecord.FILL_RESULT_SUCCESS);
        mockRecord.setUserId(1L);
        mockRecord.setFillTime(LocalDateTime.now());
        mockRecord.setCreatedTime(LocalDateTime.now());
        mockRecord.setUserName("testuser");
        mockRecord.setUserRealName("测试用户");
    }

    @Test
    void testCreateFillRecord_Success() {
        // 准备测试数据
        when(formFillRecordMapper.insert(any(FormFillRecord.class))).thenReturn(1);
        when(formFillRecordMapper.selectByIdWithDetails(any(Long.class))).thenReturn(mockRecord);

        // 执行测试
        var result = formFillRecordService.createFillRecord(mockRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(mockRequest.getRecognitionRecordId(), result.getData().getRecognitionRecordId());
        assertEquals(mockRequest.getPageBindingId(), result.getData().getPageBindingId());
        assertEquals(mockRequest.getTargetUrl(), result.getData().getTargetUrl());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).insert(any(FormFillRecord.class));
        verify(formFillRecordMapper, times(1)).selectByIdWithDetails(any(Long.class));
    }

    @Test
    void testUpdateUserFeedback_Success() {
        // 准备测试数据
        FormFillFeedbackRequest feedbackRequest = new FormFillFeedbackRequest();
        feedbackRequest.setRecordId(1L);
        feedbackRequest.setUserFeedback(FormFillRecord.FEEDBACK_LIKE);
        feedbackRequest.setFeedbackComment("很好用！");

        when(formFillRecordMapper.selectById(1L)).thenReturn(mockRecord);
        when(formFillRecordMapper.updateUserFeedback(any(), any(), any(), any())).thenReturn(1);
        when(formFillRecordMapper.selectByIdWithDetails(1L)).thenReturn(mockRecord);

        // 执行测试
        var result = formFillRecordService.updateUserFeedback(feedbackRequest);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).selectById(1L);
        verify(formFillRecordMapper, times(1)).updateUserFeedback(any(), any(), any(), any());
        verify(formFillRecordMapper, times(1)).selectByIdWithDetails(1L);
    }

    @Test
    void testUpdateUserFeedback_RecordNotFound() {
        // 准备测试数据
        FormFillFeedbackRequest feedbackRequest = new FormFillFeedbackRequest();
        feedbackRequest.setRecordId(999L);
        feedbackRequest.setUserFeedback(FormFillRecord.FEEDBACK_LIKE);

        when(formFillRecordMapper.selectById(999L)).thenReturn(null);

        // 执行测试
        var result = formFillRecordService.updateUserFeedback(feedbackRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("回填记录不存在", result.getMessage());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).selectById(999L);
        verify(formFillRecordMapper, never()).updateUserFeedback(any(), any(), any(), any());
    }

    @Test
    void testUpdateUserFeedback_UnauthorizedUser() {
        // 准备测试数据
        FormFillFeedbackRequest feedbackRequest = new FormFillFeedbackRequest();
        feedbackRequest.setRecordId(1L);
        feedbackRequest.setUserFeedback(FormFillRecord.FEEDBACK_LIKE);

        // 设置记录属于其他用户
        FormFillRecord otherUserRecord = new FormFillRecord();
        otherUserRecord.setId(1L);
        otherUserRecord.setUserId(999L); // 不同的用户ID

        when(formFillRecordMapper.selectById(1L)).thenReturn(otherUserRecord);

        // 执行测试
        var result = formFillRecordService.updateUserFeedback(feedbackRequest);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("无权限操作此记录", result.getMessage());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).selectById(1L);
        verify(formFillRecordMapper, never()).updateUserFeedback(any(), any(), any(), any());
    }

    @Test
    void testGetFillRecordById_Success() {
        // 准备测试数据
        when(formFillRecordMapper.selectByIdWithDetails(1L)).thenReturn(mockRecord);

        // 执行测试
        var result = formFillRecordService.getFillRecordById(1L);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).selectByIdWithDetails(1L);
    }

    @Test
    void testGetFillRecordById_NotFound() {
        // 准备测试数据
        when(formFillRecordMapper.selectByIdWithDetails(999L)).thenReturn(null);

        // 执行测试
        var result = formFillRecordService.getFillRecordById(999L);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("回填记录不存在", result.getMessage());

        // 验证方法调用
        verify(formFillRecordMapper, times(1)).selectByIdWithDetails(999L);
    }
}
