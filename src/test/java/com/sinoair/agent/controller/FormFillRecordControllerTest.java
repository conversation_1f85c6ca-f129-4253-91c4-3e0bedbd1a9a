package com.sinoair.agent.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.dto.request.FormFillFeedbackRequest;
import com.sinoair.agent.dto.request.FormFillRecordRequest;
import com.sinoair.agent.entity.FormFillRecord;
import com.sinoair.agent.security.JwtAuthenticationFilter;
import com.sinoair.agent.service.FormFillRecordService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 表单回填记录控制器测试类
 * 
 * <AUTHOR> Team
 */
@WebMvcTest(controllers = FormFillRecordController.class,
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = JwtAuthenticationFilter.class))
class FormFillRecordControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FormFillRecordService formFillRecordService;

    @Autowired
    private ObjectMapper objectMapper;

    private FormFillRecordRequest mockRequest;
    private FormFillFeedbackRequest mockFeedbackRequest;

    @BeforeEach
    void setUp() {
        // 设置模拟请求数据
        mockRequest = new FormFillRecordRequest();
        mockRequest.setRecognitionRecordId(1L);
        mockRequest.setPageBindingId(1L);
        mockRequest.setTargetUrl("https://example.com/form");
        mockRequest.setFillData("{\"name\":\"张三\",\"age\":30}");
        mockRequest.setFillResult(FormFillRecord.FILL_RESULT_SUCCESS);

        mockFeedbackRequest = new FormFillFeedbackRequest();
        mockFeedbackRequest.setRecordId(1L);
        mockFeedbackRequest.setUserFeedback(FormFillRecord.FEEDBACK_LIKE);
        mockFeedbackRequest.setFeedbackComment("很好用！");
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testCreateFillRecord() throws Exception {
        // 准备测试数据
        when(formFillRecordService.createFillRecord(any())).thenReturn(
                com.sinoair.agent.common.Result.success(null)
        );

        // 执行测试
        mockMvc.perform(post("/api/v1/form-fill-records")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testUpdateUserFeedback() throws Exception {
        // 准备测试数据
        when(formFillRecordService.updateUserFeedback(any())).thenReturn(
                com.sinoair.agent.common.Result.success(null)
        );

        // 执行测试
        mockMvc.perform(put("/api/v1/form-fill-records/feedback")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockFeedbackRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testGetFillRecordById() throws Exception {
        // 准备测试数据
        when(formFillRecordService.getFillRecordById(1L)).thenReturn(
                com.sinoair.agent.common.Result.success(null)
        );

        // 执行测试
        mockMvc.perform(get("/api/v1/form-fill-records/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testGetFillRecordPage() throws Exception {
        // 准备测试数据
        when(formFillRecordService.getFillRecordPage(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(com.sinoair.agent.common.Result.success(null));

        // 执行测试
        mockMvc.perform(get("/api/v1/form-fill-records/page")
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testGetFillStatistics() throws Exception {
        // 准备测试数据
        when(formFillRecordService.getFillStatistics()).thenReturn(
                com.sinoair.agent.common.Result.success(null)
        );

        // 执行测试
        mockMvc.perform(get("/api/v1/form-fill-records/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testGetRecentFillRecords() throws Exception {
        // 准备测试数据
        when(formFillRecordService.getRecentFillRecords(10)).thenReturn(
                com.sinoair.agent.common.Result.success(null)
        );

        // 执行测试
        mockMvc.perform(get("/api/v1/form-fill-records/recent")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testDeleteFillRecord() throws Exception {
        // 准备测试数据
        when(formFillRecordService.deleteFillRecord(1L)).thenReturn(
                com.sinoair.agent.common.Result.success()
        );

        // 执行测试
        mockMvc.perform(delete("/api/v1/form-fill-records/1")
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testCreateFillRecord_Unauthorized() throws Exception {
        // 执行测试（未登录）
        mockMvc.perform(post("/api/v1/form-fill-records")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(mockRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser", roles = "USER")
    void testCreateFillRecord_InvalidRequest() throws Exception {
        // 准备无效请求数据
        FormFillRecordRequest invalidRequest = new FormFillRecordRequest();
        // 缺少必填字段

        // 模拟Service返回参数错误
        when(formFillRecordService.createFillRecord(any())).thenReturn(
                com.sinoair.agent.common.Result.error(400, "请求参数不能为空")
        );

        // 执行测试
        mockMvc.perform(post("/api/v1/form-fill-records")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400));
    }
}
